# Enhanced Notification System for CarPlay Integration

## Overview

This enhanced notification system provides comprehensive notification handling across four different scenarios based on CarPlay connection status and app foreground/background state.

## Notification Scenarios

### 1. CarPlay Connected + App Foreground
- **CarPlay**: Shows AlertTemplate with actions
- **Phone**: Shows in-app alert/modal simultaneously
- **Use Case**: User is actively using the app while connected to CarPlay

### 2. CarPlay Connected + App Background  
- **CarPlay**: Shows AlertTemplate with actions
- **Phone**: Shows local notification
- **Use Case**: App is backgrounded but CarPlay is still active

### 3. CarPlay Not Connected + App Foreground
- **Phone**: Shows in-app alert/modal only
- **Use Case**: Normal app usage without CarPlay

### 4. CarPlay Not Connected + App Background
- **Phone**: Shows local notification only
- **Use Case**: App is backgrounded, no CarPlay connection

## Architecture

### Core Components

1. **NotificationManager** (`app/services/NotificationManager.js`)
   - Central notification orchestrator
   - Handles all four scenarios automatically
   - Manages app state and CarPlay connection detection
   - Provides debouncing for state changes (150ms)

2. **CarPlayWrapper** (`app/components/CarPlayWrapper.js`)
   - Conditionally renders CarPlay components based on state
   - Manages CarPlay connection detection
   - Handles foreground/background transitions

3. **CarPlayComponent** (`app/services/CarPlayComponent.js`)
   - Enhanced for foreground CarPlay scenarios
   - Integrates with NotificationManager

4. **CarPlayBackground** (`app/components/CarPlayBackground.js`)
   - Handles background CarPlay scenarios
   - Processes notifications when app is backgrounded

5. **EnhancedInAppNotification** (`app/components/EnhancedInAppNotification.js`)
   - Advanced in-app notification component
   - Supports actions, different types, and animations
   - Registers with NotificationManager for seamless integration

## Integration Points

### Existing Components Enhanced

1. **TempAlert** (`app/components/TempAlert/index.js`)
   - Now sends notifications through NotificationManager
   - Supports CarPlay AlertTemplate + phone notifications

2. **RemotePushController** (`app/components/Common/RemotePushController.js`)
   - Enhanced to use NotificationManager for push notifications
   - Maintains existing functionality while adding CarPlay support

3. **Navigation** (`app/navigation/index.js`)
   - Replaced CarPlayComponent with CarPlayWrapper
   - Added EnhancedInAppNotification component

## Usage

### Basic Notification
```javascript
import NotificationManager from '../services/NotificationManager';

NotificationManager.showNotification({
  title: 'Alert Title',
  message: 'Alert message',
  type: 'info', // 'info', 'warning', 'critical', 'success'
  priority: 'normal' // 'normal', 'critical'
});
```

### Notification with Actions
```javascript
NotificationManager.showNotification({
  title: 'Critical Alert',
  message: 'This requires immediate attention',
  type: 'critical',
  actions: [
    {
      title: 'Acknowledge',
      onPress: () => console.log('Acknowledged'),
      style: 'default'
    },
    {
      title: 'Dismiss',
      onPress: () => console.log('Dismissed'),
      style: 'cancel'
    }
  ],
  data: {
    customField: 'value',
    timestamp: new Date().toISOString()
  }
});
```

## State Management

### App State Detection
- Uses `AppState.addEventListener` with 150ms debouncing
- Prevents rapid state change issues
- Maintains refs for avoiding stale closures

### CarPlay Connection Detection
- Uses native `RNCarPlayStatus` module
- Falls back to `react-native-carplay` connection events
- Automatic connection state synchronization

## Memory Management

### Cleanup Features
- Automatic timeout clearing
- Event listener removal on unmount
- Pending notification queue management
- Proper ref cleanup

### Debouncing
- 150ms debounce for app state changes
- Prevents notification spam during rapid transitions
- Consolidates state management

## Testing

### Test Component
Use `NotificationTestComponent` (`app/components/NotificationTestComponent.js`) to validate all scenarios:

1. **Basic Notifications**: Test simple info messages
2. **Critical Alerts**: Test high-priority notifications with actions
3. **Temperature Alerts**: Test baby monitoring specific alerts
4. **Left Child Alerts**: Test emergency scenarios
5. **Multiple Notifications**: Test notification queuing

### Testing Scenarios
1. Connect/disconnect CarPlay while testing
2. Background/foreground the app during notifications
3. Test different notification types and priorities
4. Verify action button functionality

## Configuration

### Notification Types
- `info`: Standard informational notifications
- `warning`: Warning notifications (orange)
- `critical`: High-priority alerts (red)
- `success`: Success notifications (green)
- `temperature`: Baby temperature alerts
- `leftChild`: Child left in seat alerts

### Priorities
- `normal`: Standard priority
- `critical`: High priority with enhanced visibility

## Error Handling

### Fallback Mechanisms
1. If CarPlay presentation fails → Falls back to phone notification
2. If in-app notification fails → Falls back to React Native Alert
3. If all else fails → Basic Alert.alert or local notification

### Error Reporting
- Comprehensive error logging via `sendErrorReport`
- State transition tracking
- Notification delivery confirmation

## Performance Considerations

### Optimizations
- Debounced state changes prevent excessive re-renders
- Singleton NotificationManager instance
- Efficient component mounting/unmounting
- Memory leak prevention with proper cleanup

### Best Practices
- Use appropriate notification types for context
- Provide meaningful action buttons
- Include relevant data for debugging
- Test across all four scenarios

## Future Enhancements

### Potential Improvements
1. Notification persistence across app restarts
2. Custom notification sounds per type
3. Rich media support (images, videos)
4. Notification scheduling
5. User preference settings
6. Analytics and usage tracking

## Troubleshooting

### Common Issues
1. **CarPlay not detected**: Check iOS entitlements and device connection
2. **Notifications not showing**: Verify app permissions and state detection
3. **Actions not working**: Check action handler implementation
4. **Memory leaks**: Ensure proper cleanup in component unmount

### Debug Tools
- Use `NotificationTestComponent` for systematic testing
- Check console logs for state transitions
- Monitor `sendErrorReport` calls for issues
- Verify CarPlay connection status in simulator/device
