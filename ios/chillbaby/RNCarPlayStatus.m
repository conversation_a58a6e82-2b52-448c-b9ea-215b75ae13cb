#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>
#import "AppDelegate.h"

@interface RNCarPlayStatus : RCTEventEmitter <RCTBridgeModule>
@end

@implementation RNCarPlayStatus

RCT_EXPORT_MODULE();

- (NSArray<NSString *> *)supportedEvents {
  return @[@"CarPlayConnectionChanged"];
}

- (void)startObserving {
  [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(onCarPlayChanged:)
                                               name:@"CarPlayConnectionChanged"
                                             object:nil];
}

- (void)stopObserving {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)onCarPlayChanged:(NSNotification *)notification {
  BOOL connected = [notification.object boolValue];
  [self sendEventWithName:@"CarPlayConnectionChanged" body:@{ @"connected": @(connected) }];
}

RCT_REMAP_METHOD(isCarPlayConnected,
                 resolver:(RCTPromiseResolveBlock)resolve
                 rejecter:(RCTPromiseRejectBlock)reject) {
  BOOL connected = [AppDelegate shared].isCarPlayConnected;
  resolve(@(connected));
}

@end
