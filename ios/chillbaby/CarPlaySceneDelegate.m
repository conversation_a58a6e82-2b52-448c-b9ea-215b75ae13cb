#import "CarPlaySceneDelegate.h"
#import <React/RCTBridge.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTRootView.h>
#import <React/RCTEventDispatcher.h>
#import "RNCarPlay.h"  
#import "AppDelegate.h"

@implementation CarPlaySceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
           didConnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay connected - interface controller received");

    self.interfaceController = interfaceController;
    self.isCarPlayConnected = YES; // store state

    // Tell react-native-carplay CarPlay is connected
    AppDelegate *appDelegate = (AppDelegate *)UIApplication.sharedApplication.delegate;
    RCTBridge *sharedBridge = appDelegate.sharedBridge;

    // ✅ Pass shared bridge to RNCarPlay
    [RNCarPlay connectWithInterfaceController:interfaceController
                                    window:templateApplicationScene.carWindow
                                    bridge:sharedBridge];
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
        didDisconnectInterfaceController:(CPInterfaceController *)interfaceController {
    NSLog(@"CarPlay disconnected");

    // Clear the interface controller
    self.interfaceController = nil;
    self.isCarPlayConnected = NO; // store state

    // Notify React Native that CarPlay is disconnected
    [RNCarPlay disconnect];
}

@end
