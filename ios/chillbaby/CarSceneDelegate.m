#import "CarSceneDelegate.h"
#import "AppDelegate.h"
#import "RNCarPlay.h"
#import <React/RCTEventEmitter.h>
#import <React/RCTBridge.h>

@implementation CarSceneDelegate

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
           didConnectInterfaceController:(CPInterfaceController *)interfaceController {

  NSLog(@"🚗 CarPlay connected");
  self.interfaceController = interfaceController;

  [AppDelegate shared].isCarPlayConnected = YES;
  [RNCarPlay connectWithInterfaceController:interfaceController window:templateApplicationScene.carWindow];

  // Notify JS
  [[NSNotificationCenter defaultCenter] postNotificationName:@"CarPlayConnectionChanged" object:@(YES)];
}

- (void)templateApplicationScene:(CPTemplateApplicationScene *)templateApplicationScene
        didDisconnectInterfaceController:(CPInterfaceController *)interfaceController {

  NSLog(@"🚗 CarPlay disconnected");
  self.interfaceController = nil;

  [AppDelegate shared].isCarPlayConnected = NO;
  [RNCarPlay disconnect];

  [[NSNotificationCenter defaultCenter] postNotificationName:@"CarPlayConnectionChanged" object:@(NO)];
}

@end
