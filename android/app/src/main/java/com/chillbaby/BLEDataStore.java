package com.chillbaby;

import java.util.ArrayList;
import java.util.List;

public class BLEDataStore {
    private static BLEDataStore instance;

    // BLE data
    private String childName;

    // Alert data
    private boolean alertActive = false;
    private String alertTitle;
    private String alertMessage;

    // Android Auto active flag
    private boolean isActive = false;

    private final List<BLEDataChangeListener> listeners = new ArrayList<>();

    public interface BLEDataChangeListener {
        void onDataChanged();
        default void onAlertTriggered(String title, String message) {}
        default void onActiveStateChanged(boolean isActive) {}
    }

    private BLEDataStore() {}

    public static BLEDataStore getInstance() {
        if (instance == null) instance = new BLEDataStore();
        return instance;
    }

    // ✅ Unified update for BLE + alert data
    public synchronized void setData(
            String childName, boolean alertActive, String alertTitle, String alertMessage
    ) {
        boolean hasChanged = false;
        boolean alertJustActivated = !this.alertActive && alertActive;

        // Update BLE values only if they changed
        if ((this.childName == null ? childName != null : !this.childName.equals(childName))) {
            this.childName = childName;
            hasChanged = true;
        }

        // Update alert values if new alert appears
        if (alertActive != this.alertActive ||
                (alertTitle != null && !alertTitle.equals(this.alertTitle)) ||
                (alertMessage != null && !alertMessage.equals(this.alertMessage))) {
            this.alertActive = alertActive;
            this.alertTitle = alertTitle;
            this.alertMessage = alertMessage;
            hasChanged = true;

            // 🔥 If new alert is just activated, notify alert listeners
            if (alertJustActivated) {
                for (BLEDataChangeListener listener : new ArrayList<>(listeners)) {
                    listener.onAlertTriggered(alertTitle, alertMessage);
                }
            }
        }

        if (hasChanged) notifyListeners();
    }

    // ✅ Added: Separate alert activation helper (used by RNAndroidAutoManager)
    public synchronized void setAlertActive(boolean active) {
        this.alertActive = active;
        if (!active) {
            this.alertTitle = null;
            this.alertMessage = null;
        }
        notifyListeners();
    }

    // ✅ Added: Trigger a new alert manually (used by RNAndroidAutoManager)
    public synchronized void triggerAlert(String title, String message) {
        this.alertActive = true;
        this.alertTitle = title;
        this.alertMessage = message;

        for (BLEDataChangeListener listener : new ArrayList<>(listeners)) {
            listener.onAlertTriggered(title, message);
        }

        notifyListeners();
    }

    // ✅ Set active state (called from React Native)
    public synchronized void setActive(boolean active) {
        if (this.isActive != active) {
            this.isActive = active;

            // 🔥 Notify listeners that active state changed
            for (BLEDataChangeListener listener : new ArrayList<>(listeners)) {
                listener.onActiveStateChanged(active);
            }

            notifyListeners();
        }
    }

    public boolean isActive() {
        return isActive;
    }

    // ✅ Dismiss alert and update listeners
    public synchronized void dismissAlert() {
        if (alertActive) {
            alertActive = false;
            alertTitle = null;
            alertMessage = null;
            notifyListeners();
        }
    }

    // Getters
    public String getChildName() { return childName; }
    public boolean isAlertActive() { return alertActive; }
    public String getAlertTitle() { return alertTitle; }
    public String getAlertMessage() { return alertMessage; }

    // Notify all screens that data changed
    private void notifyListeners() {
        for (BLEDataChangeListener listener : new ArrayList<>(listeners)) {
            listener.onDataChanged();
        }
    }

    // Listener registration
    public void addListener(BLEDataChangeListener listener) {
        if (!listeners.contains(listener)) listeners.add(listener);
    }

    public void removeListener(BLEDataChangeListener listener) {
        listeners.remove(listener);
    }
}
