package com.chillbaby;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.car.app.CarAppService;
import androidx.car.app.CarContext;
import androidx.car.app.validation.HostValidator;
import androidx.car.app.Screen;
import androidx.car.app.Session;
import androidx.car.app.model.Action;
import androidx.car.app.model.CarColor;
import androidx.car.app.model.ItemList;
import androidx.car.app.model.ListTemplate;
import androidx.car.app.model.MessageTemplate;
import androidx.car.app.model.Row;
import androidx.car.app.model.Template;
import androidx.car.app.notification.CarAppExtender;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

/**
 * Car App Service that displays BLE data and sends alerts only to Android Auto.
 */
public class MyCarAppService extends CarAppService {

    private static final String TAG = "MyCarAppService";
    private static final String CHANNEL_ID = "android_auto_channel";

    @NonNull
    @Override
    public Session onCreateSession() {
        return new CarPlaySession();
    }

    @NonNull
    @Override
    public HostValidator createHostValidator() {
        // In development allow all hosts; tighten this for production.
        return HostValidator.ALLOW_ALL_HOSTS_VALIDATOR;
    }

    private static class CarPlaySession extends Session {
        @NonNull
        @Override
        public Screen onCreateScreen(@NonNull Intent intent) {
            return new CarPlayScreen(getCarContext());
        }
    }

    private static class CarPlayScreen extends Screen implements BLEDataStore.BLEDataChangeListener {

        private final BLEDataStore store;
        private final Handler handler = new Handler(Looper.getMainLooper());
        private final Runnable resetActive;

        CarPlayScreen(CarContext carContext) {
            super(carContext);
            store = BLEDataStore.getInstance();
            if (store != null) store.addListener(this);

            resetActive = () -> {
                if (store != null) store.setActive(false);
            };
        }

        @NonNull
        @Override
        public Template onGetTemplate() {
            if (store != null) {
                store.setActive(true);
                handler.removeCallbacks(resetActive);
                handler.postDelayed(resetActive, 2000);
            }

            if (store == null) {
                ItemList.Builder errorList = new ItemList.Builder();
                errorList.addItem(new Row.Builder()
                        .setTitle("Error")
                        .addText("BLE Data not available")
                        .build());
                return new ListTemplate.Builder()
                        .setSingleList(errorList.build())
                        .setTitle("Error")
                        .build();
            }

            if (store.isAlertActive()) {
                return new MessageTemplate.Builder(store.getAlertMessage())
                        .setTitle(store.getAlertTitle())
                        .addAction(
                                new Action.Builder()
                                        .setTitle("OK")
                                        .setOnClickListener(() -> {
                                            store.dismissAlert();
                                            invalidate();
                                        })
                                        .setBackgroundColor(CarColor.createCustom(0xFF63D8FE, 0xFF63D8FE))
                                        .build()
                        )
                        .setHeaderAction(Action.BACK)
                        .build();
            }

            ItemList.Builder itemListBuilder = new ItemList.Builder();
            itemListBuilder.addItem(new Row.Builder()
                    .setTitle("Welcome")
                    .addText(store.getChildName() != null ? store.getChildName() : "Guest")
                    .build());

            return new ListTemplate.Builder()
                    .setSingleList(itemListBuilder.build())
                    .setTitle("Baby Auto" + (store.getChildName() != null ? " - " + store.getChildName() : ""))
                    .build();
        }

        @Override
        public void onDataChanged() {
            invalidate();
        }

        @Override
        public void onAlertTriggered(String title, String message) {
            sendCarOnlyNotification(title, message);
        }

        @Override
        public void onActiveStateChanged(boolean isActive) {
            // Optional logging or behavior when screen becomes active/inactive
        }

        /**
         * Sends a notification that is intended for the car display.
         * This code runs inside CarAppService context, so it will be routed to Android Auto.
         */
        private void sendCarOnlyNotification(String title, String message) {
            try {
                CarContext carContext = getCarContext();

                // Create channel if needed (phone/car uses same channel, but since this runs in car
                // it will be shown there; ensure importance high for heads-up)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    NotificationChannel channel = new NotificationChannel(
                            CHANNEL_ID,
                            "Android Auto Alerts",
                            NotificationManager.IMPORTANCE_HIGH
                    );
                    channel.setDescription("Alerts shown in Android Auto");
                    channel.enableVibration(true);
                    NotificationManager manager = carContext.getSystemService(NotificationManager.class);
                    if (manager != null) manager.createNotificationChannel(channel);
                }

                // Build notification: use app icon (replace with your drawable if different)
                NotificationCompat.Builder builder = new NotificationCompat.Builder(carContext, CHANNEL_ID)
                        .setSmallIcon(getSmallIconResource())          // app icon
                        .setContentTitle(title)
                        .setContentText(message)
                        .setPriority(NotificationCompat.PRIORITY_HIGH)
                        .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                        .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                        .extend(new CarAppExtender.Builder()
                                .setImportance(NotificationCompat.PRIORITY_HIGH)
                                .build())
                        .setAutoCancel(true);

                // Full-screen / heads-up behavior in car:
                // Use a PendingIntent to the service so the system has an intent to show.
                Intent fullScreenIntent = new Intent(carContext, MyCarAppService.class);
                PendingIntent fullScreenPendingIntent = PendingIntent.getService(
                        carContext,
                        0,
                        fullScreenIntent,
                        PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT
                );
                builder.setFullScreenIntent(fullScreenPendingIntent, true);

                NotificationManagerCompat.from(carContext).notify((int) System.currentTimeMillis(), builder.build());
                Log.d(TAG, "✅ Car-only notification sent: " + title);
            } catch (Exception e) {
                Log.e(TAG, "❌ Error sending car-only notification", e);
            }
        }

        private int getSmallIconResource() {
            // prefer mipmap launcher icon; replace if you have a dedicated notification icon
            try {
                return getCarContext().getResources().getIdentifier("ic_launcher", "mipmap", getCarContext().getPackageName());
            } catch (Exception e) {
                // fallback to default
                return android.R.drawable.ic_dialog_alert;
            }
        }
    }
}
