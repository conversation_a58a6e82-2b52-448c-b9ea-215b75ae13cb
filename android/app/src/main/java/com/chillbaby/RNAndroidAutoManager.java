package com.chillbaby;

import android.util.Log;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

public class RNAndroidAutoManager extends ReactContextBaseJavaModule {

    private static final String TAG = "RNAndroidAutoManager";
    private final ReactApplicationContext reactContext;
    private final BLEDataStore store;

    public RNAndroidAutoManager(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        this.store = BLEDataStore.getInstance();
    }

    @Override
    public String getName() {
        return "RNAndroidAutoManager";
    }

    /**
     * Called from JS when BLE alert state changes.
     * This will only update the shared BLEDataStore and notify CarAppService.
     * No phone notifications will be shown here.
     */
    @ReactMethod
    public void updateBLEData(String childName, boolean alertActive, String alertTitle, String alertMessage) {
        Log.d(TAG, "BLEData updated: alertActive=" + alertActive);

        if (store != null) {
            store.setData(childName, alertActive, alertTitle, alertMessage);
            store.setAlertActive(alertActive);

            if (alertActive) {
                Log.d(TAG, "Triggering Car alert only (no phone notification)");
                store.triggerAlert(alertTitle, alertMessage);
            } else {
                Log.d(TAG, "Dismissing alert");
                store.dismissAlert();
            }
        } else {
            Log.e(TAG, "BLEDataStore instance is null");
        }
    }
}
