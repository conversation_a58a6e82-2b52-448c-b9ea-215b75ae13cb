apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services' 
// apply plugin: "com.bugsnag.android.gradle"
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"
apply plugin: 'com.google.gms.google-services' //for firebase

apply from: file("../../node_modules/react-native-vector-icons/fonts.gradle") //for vector icons
apply from: "../../node_modules/react-native-code-push/android/codepush.gradle" //for code-push



import com.android.build.OutputFile
import org.apache.tools.ant.taskdefs.condition.Os
import groovy.json.JsonSlurper



/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

project.ext.react = [
    enableHermes: true,  // clean and rebuild if changing
]

/**
 * Set this to true to create two separate APKs instead of one:
 *   - An APK that only works on ARM devices
 *   - An APK that only works on x86 devices
 * The advantage is the size of the APK is reduced by about 4MB.
 * Upload all the APKs to the Play Store and people will download
 * the correct one based on the CPU architecture of their device.
 */
// def enableSeparateBuildPerCPUArchitecture = true


/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = true

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

/**
 * Architectures to build native code for.
 */
def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}


// for keystore file properties
def keystorePropertiesFile = rootProject.file("keystores/release-signing.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))


// for setting version code with date
// Read package.json and return `version` field
def getVersionFromNpm() {
    //  Read and parse package.json file from project root
    def inputFile = new File("$rootDir/../package.json")
    def packageJson = new JsonSlurper().parseText(inputFile.text)


    println ("Version Name: " +  packageJson["version"]);
    // Return the version, you can get any value this way
    return packageJson["version"]
}


// Get Version Code based on current Date and Time
def versionCodeDate() {
    def versionCode = new Date().format("20yyMMddHH").toInteger()
    println("Version Code: " + versionCode)
    return versionCode
}





android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.chillbaby"
    defaultConfig {
        applicationId project.env.get("APP_ID")
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode versionCodeDate()
        versionName getVersionFromNpm()
        missingDimensionStrategy 'react-native-camera', 'general' 
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
        resValue "string", "build_config_package", "com.chillbaby"
        // manifestPlaceholders = [
        //     bugsnagApiKey: project.env.get("BUGSNAG_API_KEY")
        // ]

    }
    signingConfigs {
        // debug {
        //     storeFile file('debug.keystore')
        //     storePassword 'android'
        //     keyAlias 'androiddebugkey'
        //     keyPassword 'android'
        // }

         debug { 
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.debug
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation "androidx.car.app:app:1.2.0"
    implementation "androidx.car.app:app-projected:1.3.0"
    implementation("com.facebook.react:react-android")
    implementation("com.facebook.react:flipper-integration")
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "com.facebook.react:react-native:+"
    implementation 'com.google.firebase:firebase-analytics:17.3.0'
    implementation 'androidx.browser:browser:1.8.0'
    implementation "androidx.core:core-ktx:1.16.0"


    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

configurations.all {
    resolutionStrategy {
        force 'androidx.browser:browser:1.8.0'
    }
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
