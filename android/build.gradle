buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 29
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.9.25" // ✅ recommended for AGP 8.6.0

        googlePlayServicesVersion = "+"// default: "+" FOR react-native-geolocation-service
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.6.0")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
        classpath('com.google.gms:google-services:4.3.3') //push notify
        // classpath "com.bugsnag:bugsnag-android-gradle-plugin:7.+" // for bugsnag


    }
}

apply plugin: "com.facebook.react.rootproject"
