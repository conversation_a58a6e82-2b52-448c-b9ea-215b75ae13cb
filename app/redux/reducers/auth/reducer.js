/* eslint-disable no-console */
import types from './actions';

const initialState = {
  userData: {},
  user_id: null,
  accessToken: '',
  walkthrough: true,
  darkmode: false,
  uuid: '',
  baseColor: {},
  brandToken: '',
  leadId: null,
  notificationShow: false,
  deviceSetting: false,
  notificationData: {},
  langList: [],
  isFarenheit: false,
  userLocation: {},
  notificationCount: {},
  currentScreen: 'home',
  locationDisclouser: true,
  step7ChildInfo: false,
  closeOnboarding: false,
  step1Done: false,
  step2Done: false,
  step3Done: false,
  step4Done: false,
  step4bDone: false,
  step5Done: false,
  step5bDone: false,
  step5cDone: false,
  step5dDone: false,
  step5eDone: false,
  step5fDone: false,
  step6Done: false,
  step8Done: false,
  onBordingDone: false,
  allowBTDone: false,
};

export default function reducer(state = initialState, action) {
  switch (action.type) {
    case types.SET_DATA:
      console.log(`${types.SET_DATA} => `);
      return {
        ...state,
        userData: action.userData,
      };
    case types.SET_LANGUAGELIST:
      console.log(`${types.SET_LANGUAGELIST} => `);
      return {
        ...state,
        langList: action.langList,
      };
    case types.SET_LEAD:
      return {
        ...state,
        leadId: action.leadId,
      };
    case types.SET_NOTIFICATIONSHOW:
      return {
        ...state,
        notificationShow: action.notificationShow,
      };
    case types.SET_NOTIFICATIONDATA:
      return {
        ...state,
        notificationData: action.notificationData,
      };
    case types.SET_NOTI_COUNT:
      return {
        ...state,
        notificationCount: action.notificationCount,
      };
    case types.SET_BASECOLOR:
      return {
        ...state,
        baseColor: action.baseColor,
      };
    case types.SET_BRANDTOKEN:
      return {
        ...state,
        brandToken: action.brandToken,
      };
    case types.SET_WALKTHROUGH:
      return {
        ...state,
        walkthrough: action.walkthrough,
      };
    case types.SET_DARKMODE:
      return {
        ...state,
        darkmode: action.darkmode,
      };
    case types.SET_IS_FARENHEIT:
      return {
        ...state,
        isFarenheit: action.isFarenheit,
      };
    case types.SET_ACCESSSTOKEN:
      return {
        ...state,
        accessToken: action.accessToken,
      };
    case types.SET_USERID:
      return {
        ...state,
        user_id: action.user_id,
      };
    case types.SET_UUID:
      return {
        ...state,
        uuid: action.uuid,
      };
    case types.SET_USER_LOCATION:
      return {
        ...state,
        userLocation: action.userLocation,
      };
    case types.SET_DEVICESETTING:
      return {
        ...state,
        deviceSetting: action.deviceSetting,
      };
    case types.SET_LOCATIONDISCLOUSER:
      return {
        ...state,
        locationDisclouser: action.locationDisclouser,
      };
    case types.SET_CURRENT_SCREEN:
      return {
        ...state,
        currentScreen: action.currentScreen,
      };
    case types.SET_STEP7:
      return {
        ...state,
        step7ChildInfo: action.step7ChildInfo,
      };
    case types.SET_CLOSE_ONBORDING:
      return {
        ...state,
        closeOnboarding: action.closeOnboarding,
      };
    case types.SET_STEP1_DONE:
      return {
        ...state,
        step1Done: action.step1Done,
      };
    case types.SET_STEP2_DONE:
      return {
        ...state,
        step2Done: action.step2Done,
      };
    case types.SET_STEP3_DONE:
      return {
        ...state,
        step3Done: action.step3Done,
      };
    case types.SET_STEP4_DONE:
      return {
        ...state,
        step4Done: action.step4Done,
      };
    case types.SET_STEP4B_DONE:
      return {
        ...state,
        step4bDone: action.step4bDone,
      };
    case types.SET_STEP5_DONE:
      return {
        ...state,
        step5Done: action.step5Done,
      };
    case types.SET_STEP5B_DONE:
      return {
        ...state,
        step5bDone: action.step5bDone,
      };
    case types.SET_STEP5C_DONE:
      return {
        ...state,
        step5cDone: action.step5cDone,
      };
    case types.SET_STEP5D_DONE:
      return {
        ...state,
        step5dDone: action.step5dDone,
      };
    case types.SET_STEP5E_DONE:
      return {
        ...state,
        step5eDone: action.step5eDone,
      };
    case types.SET_STEP5F_DONE:
      return {
        ...state,
        step5fDone: action.step5fDone,
      };
    case types.SET_STEP6_DONE:
      return {
        ...state,
        step6Done: action.step6Done,
      };
    case types.SET_STEP8_DONE:
      return {
        ...state,
        step8Done: action.step8Done,
      };
    case types.SET_ONBOARDING_DONE:
      return {
        ...state,
        onBordingDone: action.onBordingDone,
      };
    case types.SET_ALLOW_BT_DONE:
      return {
        ...state,
        allowBTDone: action.allowBTDone,
      };
    case types.LOGOUT:
      return {
        ...state,
        userData: {},
        userLocation: {},
        accessToken: '',
      };
    default:
      return state;
  }
}
