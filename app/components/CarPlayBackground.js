/**
 * CarPlayBackground Component
 * Renders when app is in background AND CarPlay is connected
 * Handles background CarPlay notifications and state management
 */

import React, { useEffect, useRef } from 'react';
import { View, AppState } from 'react-native';
import { useSelector } from 'react-redux';
import NotificationManager from '../services/NotificationManager';
import { sendErrorReport } from '../utils/commonFunction';

const CarPlayBackground = () => {
  const user = useSelector(state => state.auth.userData);
  const { alertData } = useSelector(state => state.bluetooth);
  const appStateRef = useRef(AppState.currentState);
  const debounceTimeoutRef = useRef(null);

  // Monitor app state changes with debouncing
  useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Debounce state changes
      debounceTimeoutRef.current = setTimeout(() => {
        const previousState = appStateRef.current;
        appStateRef.current = nextAppState;

        sendErrorReport({
          component: 'CarPlayBackground',
          previousState,
          newState: nextAppState,
          timestamp: new Date().toISOString()
        }, 'carplay_background_app_state_change');

        // If app comes to foreground, this component should be unmounted
        // and the main CarPlay component should take over
        if (nextAppState === 'active') {
          sendErrorReport('App became active - CarPlayBackground should unmount', 'carplay_background_foreground_transition');
        }
      }, 150); // 150ms debounce
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Handle alert data changes when in background
  useEffect(() => {
    if (alertData && alertData.message && appStateRef.current !== 'active') {
      const notificationPayload = {
        title: alertData?.title || 'ChillBaby Alert',
        message: alertData?.message,
        type: alertData?.type || 'alert',
        priority: alertData?.priority || 'normal',
        actions: alertData?.actions || [],
        data: {
          source: 'CarPlayBackground',
          userId: user?.id,
          timestamp: new Date().toISOString(),
          ...alertData
        }
      };

      sendErrorReport({
        component: 'CarPlayBackground',
        notification: notificationPayload,
        appState: appStateRef.current
      }, 'carplay_background_notification_trigger');

      // Use NotificationManager to handle the notification
      // This will show CarPlay AlertTemplate + local notification
      NotificationManager.showNotification(notificationPayload);
    }
  }, [alertData, user]);

  // This component doesn't render any UI - it's just for background logic
  return <View style={{ display: 'none' }} />;
};

export default CarPlayBackground;
