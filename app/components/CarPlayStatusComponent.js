/**
 * CarPlay Status Component
 * Shows the current CarPlay connection status
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import CarPlayService from '../services/CarPlayService';

const CarPlayStatusComponent = () => {
  const [carPlayStatus, setCarPlayStatus] = useState({
    isConnected: false,
    hasActiveTemplate: false,
  });

  useEffect(() => {
    // Update status every second
    const interval = setInterval(() => {
      const status = CarPlayService.getCarPlayStatus();
      setCarPlayStatus(status);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const testCarPlayAlert = () => {
    // Test sending an alert to CarPlay
    CarPlayService.showBabyAlert({
      type: 'temperature',
      temperature: 78,
      humidity: 65,
      timestamp: new Date().toISOString(),
    });
  };

  const testCarPlayNotification = () => {
    // Test sending a notification to CarPlay
    CarPlayService.showNotification({
      title: 'Test Notification',
      message: 'This is a test CarPlay notification',
      type: 'info',
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>CarPlay Status</Text>

      <View style={styles.statusContainer}>
        <View style={styles.statusRow}>
          <Text style={styles.label}>Connection:</Text>
          <Text
            style={[
              styles.status,
              carPlayStatus.isConnected
                ? styles.connected
                : styles.disconnected,
            ]}>
            {carPlayStatus.isConnected ? 'Connected' : 'Disconnected'}
          </Text>
        </View>

        <View style={styles.statusRow}>
          <Text style={styles.label}>Template:</Text>
          <Text
            style={[
              styles.status,
              carPlayStatus.hasActiveTemplate ? styles.active : styles.inactive,
            ]}>
            {carPlayStatus.hasActiveTemplate ? 'Active' : 'Inactive'}
          </Text>
        </View>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[
            styles.button,
            !carPlayStatus.isConnected && styles.buttonDisabled,
          ]}
          onPress={testCarPlayAlert}
          disabled={!carPlayStatus.isConnected}>
          <Text style={styles.buttonText}>Test Alert</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.button,
            !carPlayStatus.isConnected && styles.buttonDisabled,
          ]}
          onPress={testCarPlayNotification}
          disabled={!carPlayStatus.isConnected}>
          <Text style={styles.buttonText}>Test Notification</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    margin: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  statusContainer: {
    marginBottom: 20,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  status: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  connected: {
    color: '#4CAF50',
  },
  disconnected: {
    color: '#F44336',
  },

  active: {
    color: '#4CAF50',
  },
  inactive: {
    color: '#9E9E9E',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  button: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
    minWidth: 120,
  },
  buttonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
  },
});

export default CarPlayStatusComponent;
