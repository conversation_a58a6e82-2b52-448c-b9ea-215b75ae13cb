/**
 * CarPlay Wrapper Component
 * Conditionally renders CarPlay components based on app state and CarPlay connection
 * 
 * Rendering Logic:
 * - App foreground + CarPlay connected: Render CarPlayComponent
 * - App background + CarPlay connected: Render CarPlayBackground
 * - CarPlay not connected: Render nothing (handled by regular app notifications)
 */

import React, { useState, useEffect, useRef } from 'react';
import { AppState, NativeEventEmitter, NativeModules } from 'react-native';
import CarPlayComponent from '../services/CarPlayComponent';
import CarPlayBackground from './CarPlayBackground';
import NotificationManager from '../services/NotificationManager';
import { sendErrorReport } from '../utils/commonFunction';

const { RNCarPlayStatus } = NativeModules;

const CarPlayWrapper = () => {
  const [carPlayConnected, setCarPlayConnected] = useState(false);
  const [appState, setAppState] = useState(AppState.currentState);
  const debounceTimeoutRef = useRef(null);
  const carPlayConnectedRef = useRef(false);
  const appStateRef = useRef(AppState.currentState);

  // Initialize CarPlay connection detection
  useEffect(() => {
    let nativeEmitter;
    let connectionListener = null;

    const initCarPlayState = async () => {
      try {
        if (RNCarPlayStatus && RNCarPlayStatus.isCarPlayConnected) {
          const connected = await RNCarPlayStatus.isCarPlayConnected();
          const isConnected = !!connected;
          
          setCarPlayConnected(isConnected);
          carPlayConnectedRef.current = isConnected;
          NotificationManager.setCarPlayConnected(isConnected);
          
          sendErrorReport({
            component: 'CarPlayWrapper',
            initialCarPlayState: isConnected,
            appState: appStateRef.current
          }, 'carplay_wrapper_init');
        }
      } catch (error) {
        console.warn('Failed to initialize CarPlay state:', error);
        sendErrorReport(error, 'carplay_wrapper_init_error');
      }

      // Subscribe to CarPlay connection changes
      if (RNCarPlayStatus && RNCarPlayStatus.addListener) {
        nativeEmitter = new NativeEventEmitter(RNCarPlayStatus);
        connectionListener = nativeEmitter.addListener(
          'CarPlayConnectionChanged',
          (event) => {
            const connected = !!event?.connected;
            
            sendErrorReport({
              component: 'CarPlayWrapper',
              carPlayConnected: connected,
              appState: appStateRef.current,
              timestamp: new Date().toISOString()
            }, 'carplay_connection_changed');

            setCarPlayConnected(connected);
            carPlayConnectedRef.current = connected;
            NotificationManager.setCarPlayConnected(connected);
          }
        );
      }
    };

    initCarPlayState();

    return () => {
      if (connectionListener) {
        connectionListener.remove();
      }
      if (nativeEmitter && nativeEmitter.removeAllListeners) {
        nativeEmitter.removeAllListeners('CarPlayConnectionChanged');
      }
    };
  }, []);

  // Monitor app state changes with debouncing
  useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Debounce state changes to prevent rapid transitions
      debounceTimeoutRef.current = setTimeout(() => {
        const previousState = appStateRef.current;
        appStateRef.current = nextAppState;
        setAppState(nextAppState);

        sendErrorReport({
          component: 'CarPlayWrapper',
          previousState,
          newState: nextAppState,
          carPlayConnected: carPlayConnectedRef.current,
          renderDecision: getRenderDecision(carPlayConnectedRef.current, nextAppState)
        }, 'carplay_wrapper_app_state_change');

      }, 150); // 150ms debounce as per memory
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Helper function to determine what to render
  const getRenderDecision = (isCarPlayConnected, currentAppState) => {
    if (!isCarPlayConnected) {
      return 'none'; // No CarPlay components needed
    }
    
    if (currentAppState === 'active') {
      return 'foreground'; // Render CarPlayComponent
    } else {
      return 'background'; // Render CarPlayBackground
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  const renderDecision = getRenderDecision(carPlayConnected, appState);

  // Log render decision for debugging
  useEffect(() => {
    sendErrorReport({
      component: 'CarPlayWrapper',
      renderDecision,
      carPlayConnected,
      appState,
      timestamp: new Date().toISOString()
    }, 'carplay_wrapper_render_decision');
  }, [renderDecision, carPlayConnected, appState]);

  // Conditional rendering based on CarPlay connection and app state
  switch (renderDecision) {
    case 'foreground':
      // App is in foreground AND CarPlay is connected
      return <CarPlayComponent />;
      
    case 'background':
      // App is in background AND CarPlay is connected
      return <CarPlayBackground />;
      
    case 'none':
    default:
      // CarPlay not connected - no CarPlay components needed
      // Regular app notifications will be handled by NotificationManager
      return null;
  }
};

export default CarPlayWrapper;
