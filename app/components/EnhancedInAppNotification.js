/**
 * Enhanced In-App Notification Component
 * Handles in-app notifications when app is in foreground
 * Integrates with NotificationManager for consistent behavior
 */

import React, { Component } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  Dimensions,
  StyleSheet,
  Platform,
  Vibration,
} from 'react-native';
import { sendErrorReport } from '../utils/commonFunction';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

class EnhancedInAppNotification extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isOpen: false,
      title: '',
      message: '',
      type: 'info',
      actions: [],
      onPress: null,
      additionalProps: {},
    };

    this.animatedValue = new Animated.Value(0);
    this.currentNotificationInterval = null;
    this.heightOffset = 0;
  }

  componentDidMount() {
    // Register this component with NotificationManager
    const NotificationManager =
      require('../services/NotificationManager').default;
    if (NotificationManager) {
      NotificationManager.setInAppNotificationRef(this);
    }
  }

  componentWillUnmount() {
    if (this.currentNotificationInterval) {
      clearTimeout(this.currentNotificationInterval);
    }
  }

  showNotification = ({
    title = '',
    message = '',
    type = 'info',
    actions = [],
    onPress = null,
    vibrate = true,
    additionalProps = {},
  }) => {
    const { closeInterval = 4000 } = this.props;

    // Clear any currently showing notification timeouts
    clearTimeout(this.currentNotificationInterval);

    // Vibrate if enabled and supported
    if (vibrate && Platform.OS === 'ios') {
      Vibration.vibrate();
    } else if (vibrate && Platform.OS === 'android') {
      Vibration.vibrate(200);
    }

    sendErrorReport(
      {
        title,
        type,
        hasActions: actions.length > 0,
        component: 'EnhancedInAppNotification',
      },
      'in_app_notification_show',
    );

    const showNotificationWithStateChanges = () => {
      this.setState(
        {
          isOpen: true,
          title,
          message,
          type,
          actions,
          onPress,
          additionalProps,
        },
        () => {
          this.showAnimation(() => {
            this.currentNotificationInterval = setTimeout(() => {
              this.hideNotification();
            }, closeInterval);
          });
        },
      );
    };

    if (this.state.isOpen) {
      this.hideNotification(() => {
        setTimeout(showNotificationWithStateChanges, 100);
      });
    } else {
      showNotificationWithStateChanges();
    }
  };

  hideNotification = callback => {
    this.setState(
      {
        isOpen: false,
        title: '',
        message: '',
        type: 'info',
        actions: [],
        onPress: null,
        additionalProps: {},
      },
      () => {
        this.hideAnimation(callback);
      },
    );
  };

  showAnimation = callback => {
    Animated.timing(this.animatedValue, {
      toValue: 1,
      duration: 300,
      useNativeDriver: false,
    }).start(callback);
  };

  hideAnimation = callback => {
    Animated.timing(this.animatedValue, {
      toValue: 0,
      duration: 300,
      useNativeDriver: false,
    }).start(callback);
  };

  handlePress = () => {
    const { onPress } = this.state;
    if (onPress) {
      onPress();
    }
    this.hideNotification();
  };

  handleActionPress = action => {
    if (action.onPress) {
      action.onPress();
    }
    this.hideNotification();
  };

  getNotificationStyle = () => {
    const { type } = this.state;

    switch (type) {
      case 'critical':
      case 'error':
        return styles.criticalNotification;
      case 'warning':
        return styles.warningNotification;
      case 'success':
        return styles.successNotification;
      default:
        return styles.infoNotification;
    }
  };

  render() {
    const {
      backgroundColour = '#333333',
      baseHeight = 80,
      topOffset = Platform.OS === 'ios' ? 44 : 0,
    } = this.props;

    const { animatedValue, title, message, type, actions, isOpen } = this.state;

    if (!isOpen) {
      return null;
    }

    const height =
      baseHeight + this.heightOffset + (actions.length > 0 ? 50 : 0);
    const notificationStyle = this.getNotificationStyle();

    return (
      <Animated.View
        style={[
          styles.notification,
          notificationStyle,
          {
            height,
            backgroundColor: backgroundColour,
            top: topOffset,
          },
          {
            transform: [
              {
                translateY: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-height, 0],
                }),
              },
            ],
          },
        ]}>
        <TouchableOpacity
          style={styles.notificationContent}
          onPress={this.handlePress}
          activeOpacity={0.8}>
          <View style={styles.textContainer}>
            <Text style={styles.title} numberOfLines={1}>
              {title}
            </Text>
            <Text style={styles.message} numberOfLines={2}>
              {message}
            </Text>
          </View>

          {actions.length > 0 && (
            <View style={styles.actionsContainer}>
              {actions.slice(0, 2).map((action, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.actionButton,
                    action.style === 'destructive' && styles.destructiveAction,
                  ]}
                  onPress={() => this.handleActionPress(action)}>
                  <Text
                    style={[
                      styles.actionText,
                      action.style === 'destructive' &&
                        styles.destructiveActionText,
                    ]}>
                    {action.title || action.text || 'OK'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  }
}

const styles = StyleSheet.create({
  notification: {
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 9999,
    borderRadius: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  infoNotification: {
    backgroundColor: '#333333',
  },
  criticalNotification: {
    backgroundColor: '#FF3B30',
  },
  warningNotification: {
    backgroundColor: '#FF9500',
  },
  successNotification: {
    backgroundColor: '#34C759',
  },
  notificationContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  actionsContainer: {
    flexDirection: 'row',
    marginTop: 8,
    justifyContent: 'flex-end',
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginLeft: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 6,
  },
  destructiveAction: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  actionText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  destructiveActionText: {
    color: '#FF3B30',
  },
});

export default EnhancedInAppNotification;
