/**
 * Notification Test Component
 * Test component to validate all four notification scenarios
 * 
 * Scenarios to test:
 * 1. CarPlay connected + App foreground: CarPlay AlertTemplate + Phone alert/modal
 * 2. CarPlay connected + App background: CarPlay AlertTemplate + Phone local notification
 * 3. CarPlay not connected + App foreground: Phone alert/modal only
 * 4. CarPlay not connected + App background: Phone local notification only
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  AppState,
  Alert,
} from 'react-native';
import NotificationManager from '../services/NotificationManager';
import { sendErrorReport } from '../utils/commonFunction';

const NotificationTestComponent = () => {
  const [appState, setAppState] = useState(AppState.currentState);
  const [carPlayConnected, setCarPlayConnected] = useState(false);
  const [testResults, setTestResults] = useState([]);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      setAppState(nextAppState);
    });

    return () => subscription?.remove();
  }, []);

  const addTestResult = (test, result, details = '') => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, {
      id: Date.now(),
      test,
      result,
      details,
      timestamp,
      appState,
      carPlayConnected
    }]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  // Test different notification types
  const testBasicNotification = () => {
    const notification = {
      title: '🔔 Basic Test Notification',
      message: 'This is a basic test notification to verify the system works.',
      type: 'info',
      priority: 'normal'
    };

    try {
      NotificationManager.showNotification(notification);
      addTestResult('Basic Notification', 'SUCCESS', 'Basic notification sent');
      sendErrorReport({
        test: 'basic_notification',
        appState,
        carPlayConnected
      }, 'notification_test_basic');
    } catch (error) {
      addTestResult('Basic Notification', 'ERROR', error.message);
    }
  };

  const testCriticalAlert = () => {
    const notification = {
      title: '🚨 Critical Alert Test',
      message: 'This is a critical alert that should show prominently on both CarPlay and phone.',
      type: 'critical',
      priority: 'critical',
      actions: [
        {
          title: 'Acknowledge',
          onPress: () => {
            addTestResult('Critical Alert Action', 'SUCCESS', 'Acknowledge button pressed');
            Alert.alert('Test', 'Acknowledge button was pressed!');
          }
        },
        {
          title: 'Dismiss',
          onPress: () => {
            addTestResult('Critical Alert Action', 'SUCCESS', 'Dismiss button pressed');
          },
          style: 'cancel'
        }
      ]
    };

    try {
      NotificationManager.showNotification(notification);
      addTestResult('Critical Alert', 'SUCCESS', 'Critical alert sent with actions');
      sendErrorReport({
        test: 'critical_alert',
        appState,
        carPlayConnected
      }, 'notification_test_critical');
    } catch (error) {
      addTestResult('Critical Alert', 'ERROR', error.message);
    }
  };

  const testTemperatureAlert = () => {
    const notification = {
      title: '🌡️ Temperature Alert',
      message: 'Baby temperature is 38.5°C - Check immediately!',
      type: 'temperature',
      priority: 'critical',
      actions: [
        {
          title: 'View Details',
          onPress: () => {
            addTestResult('Temperature Alert Action', 'SUCCESS', 'View Details pressed');
            Alert.alert('Temperature Details', 'Current: 38.5°C\nNormal Range: 36-37°C');
          }
        }
      ],
      data: {
        temperature: 38.5,
        deviceId: 'test-device-001',
        alertType: 'temperature_high'
      }
    };

    try {
      NotificationManager.showNotification(notification);
      addTestResult('Temperature Alert', 'SUCCESS', 'Temperature alert sent');
      sendErrorReport({
        test: 'temperature_alert',
        appState,
        carPlayConnected,
        temperature: 38.5
      }, 'notification_test_temperature');
    } catch (error) {
      addTestResult('Temperature Alert', 'ERROR', error.message);
    }
  };

  const testLeftChildAlert = () => {
    const notification = {
      title: '👶 Child Left in Seat',
      message: 'Child detected in seat for 30+ seconds. Take immediate action.',
      type: 'leftChild',
      priority: 'critical',
      actions: [
        {
          title: 'Send SMS',
          onPress: () => {
            addTestResult('Left Child Action', 'SUCCESS', 'Send SMS pressed');
            Alert.alert('SMS Sent', 'Emergency SMS has been sent to emergency contacts.');
          }
        },
        {
          title: 'I\'m Here',
          onPress: () => {
            addTestResult('Left Child Action', 'SUCCESS', 'I\'m Here pressed');
            Alert.alert('Alert Cleared', 'Child safety alert has been acknowledged.');
          },
          style: 'cancel'
        }
      ],
      data: {
        alertType: 'child_left_in_seat',
        duration: 35,
        deviceId: 'test-device-001'
      }
    };

    try {
      NotificationManager.showNotification(notification);
      addTestResult('Left Child Alert', 'SUCCESS', 'Left child alert sent');
      sendErrorReport({
        test: 'left_child_alert',
        appState,
        carPlayConnected
      }, 'notification_test_left_child');
    } catch (error) {
      addTestResult('Left Child Alert', 'ERROR', error.message);
    }
  };

  const testMultipleNotifications = () => {
    const notifications = [
      {
        title: '📱 Test 1',
        message: 'First notification in sequence',
        type: 'info'
      },
      {
        title: '📱 Test 2',
        message: 'Second notification in sequence',
        type: 'warning'
      },
      {
        title: '📱 Test 3',
        message: 'Third notification in sequence',
        type: 'success'
      }
    ];

    try {
      notifications.forEach((notification, index) => {
        setTimeout(() => {
          NotificationManager.showNotification(notification);
        }, index * 1000);
      });
      addTestResult('Multiple Notifications', 'SUCCESS', '3 notifications sent with 1s delay');
    } catch (error) {
      addTestResult('Multiple Notifications', 'ERROR', error.message);
    }
  };

  const getCurrentScenario = () => {
    if (carPlayConnected && appState === 'active') {
      return 'CarPlay + Foreground';
    } else if (carPlayConnected && appState !== 'active') {
      return 'CarPlay + Background';
    } else if (!carPlayConnected && appState === 'active') {
      return 'Phone + Foreground';
    } else {
      return 'Phone + Background';
    }
  };

  const getScenarioColor = () => {
    const scenario = getCurrentScenario();
    switch (scenario) {
      case 'CarPlay + Foreground': return '#4CAF50';
      case 'CarPlay + Background': return '#2196F3';
      case 'Phone + Foreground': return '#FF9800';
      case 'Phone + Background': return '#9C27B0';
      default: return '#757575';
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🧪 Notification System Test</Text>
        
        <View style={[styles.scenarioCard, { borderColor: getScenarioColor() }]}>
          <Text style={styles.scenarioTitle}>Current Scenario</Text>
          <Text style={[styles.scenarioText, { color: getScenarioColor() }]}>
            {getCurrentScenario()}
          </Text>
          <Text style={styles.statusText}>
            App State: {appState} | CarPlay: {carPlayConnected ? 'Connected' : 'Disconnected'}
          </Text>
        </View>
      </View>

      <View style={styles.controlsSection}>
        <Text style={styles.sectionTitle}>🎛️ Test Controls</Text>
        
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={testBasicNotification}
        >
          <Text style={styles.buttonText}>Test Basic Notification</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.criticalButton]}
          onPress={testCriticalAlert}
        >
          <Text style={styles.buttonText}>Test Critical Alert</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.warningButton]}
          onPress={testTemperatureAlert}
        >
          <Text style={styles.buttonText}>Test Temperature Alert</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.emergencyButton]}
          onPress={testLeftChildAlert}
        >
          <Text style={styles.buttonText}>Test Left Child Alert</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={testMultipleNotifications}
        >
          <Text style={styles.buttonText}>Test Multiple Notifications</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsSection}>
        <View style={styles.resultsHeader}>
          <Text style={styles.sectionTitle}>📊 Test Results</Text>
          <TouchableOpacity onPress={clearResults} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>

        {testResults.length === 0 ? (
          <Text style={styles.noResultsText}>No test results yet. Run some tests!</Text>
        ) : (
          testResults.slice(-10).reverse().map((result) => (
            <View key={result.id} style={styles.resultCard}>
              <View style={styles.resultHeader}>
                <Text style={styles.resultTest}>{result.test}</Text>
                <Text style={[
                  styles.resultStatus,
                  result.result === 'SUCCESS' ? styles.successStatus : styles.errorStatus
                ]}>
                  {result.result}
                </Text>
              </View>
              <Text style={styles.resultDetails}>{result.details}</Text>
              <Text style={styles.resultMeta}>
                {result.timestamp} | {result.appState} | CarPlay: {result.carPlayConnected ? 'On' : 'Off'}
              </Text>
            </View>
          ))
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  scenarioCard: {
    padding: 15,
    borderRadius: 10,
    borderWidth: 2,
    backgroundColor: '#fafafa',
  },
  scenarioTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  scenarioText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  statusText: {
    fontSize: 14,
    color: '#666',
  },
  controlsSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#2196F3',
  },
  criticalButton: {
    backgroundColor: '#F44336',
  },
  warningButton: {
    backgroundColor: '#FF9800',
  },
  emergencyButton: {
    backgroundColor: '#E91E63',
  },
  secondaryButton: {
    backgroundColor: '#9C27B0',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsSection: {
    padding: 20,
    paddingTop: 0,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  clearButton: {
    padding: 8,
    backgroundColor: '#757575',
    borderRadius: 5,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  noResultsText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    padding: 20,
  },
  resultCard: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  resultTest: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  resultStatus: {
    fontSize: 14,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  successStatus: {
    backgroundColor: '#E8F5E8',
    color: '#4CAF50',
  },
  errorStatus: {
    backgroundColor: '#FFEBEE',
    color: '#F44336',
  },
  resultDetails: {
    fontSize: 14,
    color: '#333',
    marginBottom: 5,
  },
  resultMeta: {
    fontSize: 12,
    color: '#666',
  },
});

export default NotificationTestComponent;
