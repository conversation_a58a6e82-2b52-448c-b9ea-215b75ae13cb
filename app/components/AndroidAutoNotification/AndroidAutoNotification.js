import React, { useEffect } from 'react';
import { NativeModules } from 'react-native';
import { useSelector } from 'react-redux';

const { RNAndroidAutoManager } = NativeModules;

const AndroidAutoComponent = () => {
  const alertData = useSelector(state => state.bluetooth.alertData);
  const activeChildDetail = useSelector(
    state => state.bluetooth.activeChildDetail,
  );
  const user = useSelector(state => state.auth.userData);

  useEffect(() => {
    if (!RNAndroidAutoManager) {
      console.warn('⚠️ RNAndroidAutoManager is not available!');
      return;
    }

    const updateAndroidAuto = async () => {
      try {
        const isActive = await RNAndroidAutoManager.isAppActive();
        console.log('🚀 isAppActive:', isActive);

        const childName = user?.full_name || activeChildDetail?.name || 'Guest';
        const alertActive = !!alertData?.message;
        const alertTitle = alertData?.carPlayTitle || 'Alert!';
        const alertMessage = alertData?.message || '';

        // Update BLE data normally
        RNAndroidAutoManager.updateBLEData(
          childName,
          alertActive,
          alertTitle,
          alertMessage,
        );
      } catch (error) {
        console.error('❌ Error updating Android Auto:', error);
      }
    };

    updateAndroidAuto();
  }, [alertData, activeChildDetail, user]);

  return null;
};

export default AndroidAutoComponent;
