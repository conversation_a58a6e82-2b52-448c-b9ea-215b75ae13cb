/* eslint-disable quotes */
import 'react-native-gesture-handler';
import React, { useEffect, useRef, useState } from 'react';
import {
  DarkTheme,
  DefaultTheme,
  NavigationContainer,
} from '@react-navigation/native';
import {
  CardStyleInterpolators,
  createStackNavigator,
  HeaderStyleInterpolators,
  TransitionSpecs,
} from '@react-navigation/stack';
import DatePicker from '@react-native-community/datetimepicker';
import {
  View,
  TextInput,
  Text,
  Alert,
  Dimensions,
  Platform,
} from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { useDispatch, useSelector } from 'react-redux';
import { EventRegister } from 'react-native-event-listeners';
import Config from 'react-native-config';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Torch from 'react-native-torch';
import RedirectLS from '../screens/RedirectLS';
import ForgotPassword from '../screens/ForgotPassword';
import Otp from '../screens/Otp';
import SplashScreen from '../screens/SplashScreen';
import Walkthrough from '../screens/Walkthrough';
import Login from '../screens/Login';
import Signup from '../screens/Signup';
import Gender from '../screens/Gender/index';
import ChildInfo from '../screens/ChildInfo/index';
import Dashboard from '../screens/Dashboard';
import Home from '../screens/Home';
import Devices from '../screens/Devices';
import TabBar from './TabBar';
import Setting from '../screens/Setting/index';
import Alerts from '../screens/Alert/index';
import CDeviceList from '../screens/ConDeviceList/index';
import Connect from '../screens/Connect';
import QRScanner from '../screens/QRScanner';
import ChatScreen from '../screens/ChatScreen';
import { translate } from '../lang/Translate';
import UpdatePassword from '../screens/UpdatePassword/index';
import PushNotification from '../components/PushNotification';
import FAQScreen from '../screens/FAQScreen';
import EditDevice from '../screens/EditDevice';
import NetworkModal from '../screens/NetworkModal';
import Products from '../screens/Products';
import ProductCatalouge from '../screens/ProductCatalouge';
import AuthAction from '../redux/reducers/auth/actions';
import BluetoothAction from '../redux/reducers/bluetooth/actions';
import BaseColor, { DarkBaseColor } from '../config/colors';
import ProductDetail from '../screens/ProductDetail';
import QRCodeDetail from '../screens/QRCodeDetail';
import { getApiData } from '../utils/apiHelper';
import BaseSetting from '../config/setting';
import CAlert from '../components/CAlert';
import BleList from '../screens/DeviceList';
import TempAlert from '../components/TempAlert';
import MyQRcode from '../screens/MyQRcode';
import { getLocationAndStore, sendErrorReport } from '../utils/commonFunction';
import UserManual from '../screens/UserManual';
// import OTAUpdate from '../screens/OTAUpdate';
// import OTAWeb from '../screens/OTAUpdate/OTAWeb';
import Troubleshoot from '../screens/Troubleshoot';
import RemotePushController from '../components/Common/RemotePushController';
import AndroidAutoComponent from '../components/AndroidAutoNotification/AndroidAutoNotification';
import CarPlayWrapper from '../components/CarPlayWrapper';
import EnhancedInAppNotification from '../components/EnhancedInAppNotification';
import { navigationRef } from './RootNavigation';

// Remove font scale
Text.defaultProps = Text.defaultProps || {};
Text.defaultProps.allowFontScaling = false;
TextInput.defaultProps = TextInput.defaultProps || {};
TextInput.defaultProps.allowFontScaling = false;
// DatePicker.defaultProps = DatePicker.defaultProps || {};
// DatePicker.defaultProps.allowFontScaling = false;

const NavStart = () => {
  const dispatch = useDispatch();
  const { setBaseColor, setDarkmode, setBrandToken } = AuthAction;
  const darkmode = useSelector(state => state.auth.darkmode);
  const accessToken = useSelector(state => state.auth.accessToken);
  const locationDisclouser = useSelector(
    state => state.auth.locationDisclouser,
  );
  if (darkmode === false) {
    dispatch(setBaseColor(BaseColor));
  } else {
    dispatch(setBaseColor(DarkBaseColor));
  }

  const Stack = createStackNavigator();
  const Tab = createBottomTabNavigator();
  const Drawer = createDrawerNavigator();

  // const navigationRef = useRef();

  useEffect(() => {
    AsyncStorage.setItem('isConnect', 'fal');
    // Torch.switchState(false);
    dispatch(BluetoothAction.setIsBleConnected(false));
    dispatch(BluetoothAction.setRequestMTU(true));
    dispatch(BluetoothAction.setBleData({}));
    dispatch(BluetoothAction.setHomeDeviceClick(false));
    dispatch(BluetoothAction.setIsConnectLoad(false));

    const eventListener = EventRegister.addEventListener(
      'changeAppTheme',
      data => {
        setdarkApp(data);
        setDarkmode(data);
      },
    );
    return () => {
      EventRegister.removeEventListener(eventListener);
    };
  }, []);
  useEffect(() => {
    console.log('in naviga----', locationDisclouser);
    if (!locationDisclouser) {
      console.log('in if naviga----', locationDisclouser);
      getLocationAndStore();
    }
  }, [locationDisclouser]);

  const [darkApp, setdarkApp] = useState(darkmode);

  const lightTheme = {
    ...DefaultTheme,
    colors: {
      ...BaseColor,
      ulla: '#ff0000',
      background: '#fff',
    },
  };

  const darkTheme = {
    ...DarkTheme,
    colors: {
      ...DarkBaseColor,
      ulla: '#ff0000',
      background: '#fff',
    },
  };

  const appTheme = darkApp ? darkTheme : lightTheme;

  const sendPackage = () => {
    const data = {
      package_name: 'com.chillbaby',
    };

    const headers = {
      'Content-Type': 'application/json',
      // authorization: accessToken ? `Bearer ${accessToken}` : "",
    };

    getApiData(BaseSetting.endpoints.sendPackage, 'post', data, headers)
      .then(respose => {
        console.log('RESPOSE ====>>>>', respose);
        if (respose.brand_token) {
          console.log('RESPOSE ====>>>>', respose.brand_token);
          dispatch(setBrandToken(respose.brand_token));
        }
      })
      .catch(err => {
        console.log('ERROR', err);
        sendErrorReport(err, 'send_token_package');
      });
  };

  useEffect(() => {
    // if (accessToken !== "") {
    sendPackage();
    // }
  }, [accessToken]);

  // messaging().onNotificationOpenedApp((remoteMessage) => {
  //   // navigation.navigate(remoteMessage.data.type);
  //   // Alert.alert(remoteMessage.data);
  // });

  const DashboardNavigationTabs = () => (
    <Tab.Navigator
      tabBar={props => (
        <View
          style={{
            backgroundColor: '#0000',
            position: 'absolute',
            bottom: 0,
            right: 0,
            left: 0,
          }}>
          <TabBar {...props} />
        </View>
      )}
      initialRouteName={translate('home')}>
      <Tab.Screen
        name={translate('devices')}
        component={Devices}
        options={{ headerShown: false }}
      />
      <Tab.Screen
        name={translate('dashboard')}
        component={Dashboard}
        options={{ headerShown: false }}
      />
      <Tab.Screen
        name={translate('home')}
        component={Home}
        options={{ headerShown: false }}
      />
    </Tab.Navigator>
  );

  const DrawerNav = () => (
    <Drawer.Navigator
      initialRouteName="Home"
      screenOptions={{
        drawerStyle: { width: '100%' },
      }}
      drawerContent={props => <Setting {...props} />}
      openByDefault={false}>
      <Drawer.Screen
        name="Dashboard"
        component={DashboardNavigationTabs}
        options={{ headerShown: false }}
      />
    </Drawer.Navigator>
  );
  return (
    <>
      <NavigationContainer
        ref={navigationRef}
        theme={appTheme}
        initialRouteName="Otp">
        <Stack.Navigator
          screenOptions={{
            cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
          }}>
          <Stack.Screen
            name="SplashScreen"
            component={SplashScreen}
            options={{ headerShown: false, gestureEnabled: false }}
          />
          <Stack.Screen
            name="Walkthrough"
            component={Walkthrough}
            options={{ headerShown: false, gestureEnabled: false }}
          />
          <Stack.Screen
            name="RedirectLS"
            component={RedirectLS}
            options={{ headerShown: false, gestureEnabled: false }}
          />
          <Stack.Screen
            name="Login"
            component={Login}
            options={{
              headerShown: false,
              cardStyleInterpolator:
                CardStyleInterpolators.forRevealFromBottomAndroid,
            }}
          />
          <Stack.Screen
            name="Signup"
            component={Signup}
            options={{
              headerShown: false,
              cardStyleInterpolator:
                CardStyleInterpolators.forRevealFromBottomAndroid,
            }}
          />
          <Stack.Screen
            name="ForgotPassword"
            component={ForgotPassword}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Otp"
            component={Otp}
            options={{ headerShown: false, gestureEnabled: false }}
            initialParams={{ login: false }}
          />
          <Stack.Screen
            name="UpdatePassword"
            component={UpdatePassword}
            options={{ headerShown: false, gestureEnabled: false }}
          />
          <Stack.Screen
            name="Gender"
            component={Gender}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="ChildInfo"
            component={ChildInfo}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="DrawerNav"
            component={DrawerNav}
            options={{
              headerShown: false,
              gestureEnabled: false,
              animationEnabled: false,
            }}
          />
          <Stack.Screen
            name="Alerts"
            component={Alerts}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="CDeviceList"
            component={CDeviceList}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Connect"
            component={Connect}
            options={{ headerShown: false, gestureEnabled: false }}
          />
          <Stack.Screen
            name="QRScanner"
            component={QRScanner}
            options={{ headerShown: false, gestureEnabled: false }}
          />
          <Stack.Screen
            name="bleList"
            component={BleList}
            options={{ headerShown: false, gestureEnabled: false }}
          />
          <Stack.Screen
            name="ChatScreen"
            component={ChatScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="FAQScreen"
            component={FAQScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="EditDevice"
            component={EditDevice}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="ProductCatalouge"
            component={ProductCatalouge}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Products"
            component={Products}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="ProductDetail"
            component={ProductDetail}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="QRCodeDetail"
            component={QRCodeDetail}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="MyQRcode"
            component={MyQRcode}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="UserManual"
            component={UserManual}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Troubleshoot"
            component={Troubleshoot}
            options={{ headerShown: false }}
          />
          {/* <Stack.Screen
            name="OTAUpdate"
            component={OTAUpdate}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="OTAWeb"
            component={OTAWeb}
            options={{ headerShown: false }}
          /> */}
        </Stack.Navigator>
        {/* <PushNotification
          onConnect={() => {
            console.log('On Connect');
          }}
          navigation={navigationRef}
        /> */}
        <RemotePushController />
        <NetworkModal />
        <TempAlert navigation={navigationRef} />
        <EnhancedInAppNotification closeInterval={4000} />
        {Platform.OS === 'android' ? (
          <AndroidAutoComponent />
        ) : (
          <CarPlayWrapper />
        )}
      </NavigationContainer>
    </>
  );
};

export default NavStart;
