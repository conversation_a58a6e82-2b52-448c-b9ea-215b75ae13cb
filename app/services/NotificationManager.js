/**
 * Enhanced Notification Manager
 * Handles all notification scenarios based on CarPlay connection and app state
 * 
 * Scenarios:
 * 1. CarPlay connected + App foreground: CarPlay AlertTemplate + Phone alert/modal
 * 2. CarPlay connected + App background: CarPlay AlertTemplate + Phone local notification
 * 3. CarPlay not connected + App foreground: Phone alert/modal only
 * 4. CarPlay not connected + App background: Phone local notification only
 */

import React from 'react';
import { AppState, Alert, Platform } from 'react-native';
import { CarPlay, AlertTemplate, ActionSheetTemplate } from 'react-native-carplay';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import PushNotification from 'react-native-push-notification';
import { sendErrorReport } from '../utils/commonFunction';

class NotificationManager {
  constructor() {
    this.carPlayConnected = false;
    this.appState = AppState.currentState;
    this.debounceTimeout = null;
    this.pendingNotifications = [];
    this.inAppNotificationRef = null;
    
    // Initialize app state listener
    this.initializeAppStateListener();
    
    // Debounce delay for state changes (prevents rapid state changes)
    this.debounceDelay = 150;
  }

  /**
   * Initialize app state listener with debouncing
   */
  initializeAppStateListener() {
    this.appStateSubscription = AppState.addEventListener('change', (nextAppState) => {
      // Clear existing timeout
      if (this.debounceTimeout) {
        clearTimeout(this.debounceTimeout);
      }
      
      // Debounce state changes
      this.debounceTimeout = setTimeout(() => {
        const previousState = this.appState;
        this.appState = nextAppState;
        
        sendErrorReport({
          previousState,
          newState: nextAppState,
          carPlayConnected: this.carPlayConnected
        }, 'notification_manager_app_state_change');
        
        // Process any pending notifications after state change
        this.processPendingNotifications();
      }, this.debounceDelay);
    });
  }

  /**
   * Update CarPlay connection status
   */
  setCarPlayConnected(connected) {
    const wasConnected = this.carPlayConnected;
    this.carPlayConnected = connected;
    
    sendErrorReport({
      wasConnected,
      nowConnected: connected,
      appState: this.appState
    }, 'notification_manager_carplay_state_change');
    
    // Process pending notifications when CarPlay connects
    if (connected && !wasConnected) {
      this.processPendingNotifications();
    }
  }

  /**
   * Set reference to in-app notification component
   */
  setInAppNotificationRef(ref) {
    this.inAppNotificationRef = ref;
  }

  /**
   * Main notification handler - determines which scenario to use
   */
  async showNotification(notification) {
    try {
      const {
        title,
        message,
        type = 'info',
        actions = [],
        priority = 'normal',
        data = {}
      } = notification;

      const isCarPlayConnected = this.carPlayConnected;
      const isForeground = this.appState === 'active';

      sendErrorReport({
        title,
        type,
        priority,
        isCarPlayConnected,
        isForeground,
        scenario: this.getScenarioName(isCarPlayConnected, isForeground)
      }, 'notification_manager_show_notification');

      // Scenario 1: CarPlay connected + App foreground
      if (isCarPlayConnected && isForeground) {
        await this.handleCarPlayForegroundScenario({ title, message, type, actions, data });
      }
      // Scenario 2: CarPlay connected + App background  
      else if (isCarPlayConnected && !isForeground) {
        await this.handleCarPlayBackgroundScenario({ title, message, type, actions, data });
      }
      // Scenario 3: CarPlay not connected + App foreground
      else if (!isCarPlayConnected && isForeground) {
        await this.handlePhoneForegroundScenario({ title, message, type, actions, data });
      }
      // Scenario 4: CarPlay not connected + App background
      else {
        await this.handlePhoneBackgroundScenario({ title, message, type, actions, data });
      }

    } catch (error) {
      console.error('❌ NotificationManager error:', error);
      sendErrorReport(error, 'notification_manager_error');
      
      // Fallback to basic phone notification
      this.fallbackNotification(notification);
    }
  }

  /**
   * Scenario 1: CarPlay connected + App foreground
   * Shows AlertTemplate on CarPlay AND alert/modal on phone
   */
  async handleCarPlayForegroundScenario({ title, message, type, actions, data }) {
    try {
      // Show on CarPlay
      await this.showCarPlayAlert({ title, message, type, actions, data });
      
      // Also show on phone (in-app)
      await this.showPhoneAlert({ title, message, type, actions, data });
      
      sendErrorReport('success', 'carplay_foreground_scenario');
    } catch (error) {
      console.error('❌ CarPlay foreground scenario error:', error);
      // Fallback to phone only
      await this.showPhoneAlert({ title, message, type, actions, data });
    }
  }

  /**
   * Scenario 2: CarPlay connected + App background
   * Shows AlertTemplate on CarPlay AND local notification on phone
   */
  async handleCarPlayBackgroundScenario({ title, message, type, actions, data }) {
    try {
      // Show on CarPlay
      await this.showCarPlayAlert({ title, message, type, actions, data });
      
      // Also show local notification on phone
      this.showLocalNotification({ title, message, type, data });
      
      sendErrorReport('success', 'carplay_background_scenario');
    } catch (error) {
      console.error('❌ CarPlay background scenario error:', error);
      // Fallback to local notification only
      this.showLocalNotification({ title, message, type, data });
    }
  }

  /**
   * Scenario 3: CarPlay not connected + App foreground
   * Shows alert/modal on phone only
   */
  async handlePhoneForegroundScenario({ title, message, type, actions, data }) {
    await this.showPhoneAlert({ title, message, type, actions, data });
    sendErrorReport('success', 'phone_foreground_scenario');
  }

  /**
   * Scenario 4: CarPlay not connected + App background
   * Shows local notification on phone only
   */
  async handlePhoneBackgroundScenario({ title, message, type, actions, data }) {
    this.showLocalNotification({ title, message, type, data });
    sendErrorReport('success', 'phone_background_scenario');
  }

  /**
   * Show alert on CarPlay using AlertTemplate
   */
  async showCarPlayAlert({ title, message, type, actions, data }) {
    try {
      const alertActions = actions.map((action, index) => ({
        id: `action_${index}`,
        title: action.title || action.text || 'OK',
        style: action.style || 'default',
        handler: () => {
          if (action.onPress) {
            action.onPress();
          }
          // Dismiss the template after action
          CarPlay.dismissTemplate();
        }
      }));

      // Add default OK action if no actions provided
      if (alertActions.length === 0) {
        alertActions.push({
          id: 'default_ok',
          title: 'OK',
          style: 'default',
          handler: () => CarPlay.dismissTemplate()
        });
      }

      const alertTemplate = new AlertTemplate({
        titleVariants: [title || 'ChillBaby Alert'],
        subtitleVariants: [message || ''],
        actions: alertActions
      });

      await CarPlay.presentTemplate(alertTemplate, true);
      sendErrorReport('success', 'carplay_alert_presented');
      
    } catch (error) {
      console.error('❌ Failed to show CarPlay alert:', error);
      sendErrorReport(error, 'carplay_alert_error');
      throw error;
    }
  }

  /**
   * Show alert on phone (in-app modal/alert)
   */
  async showPhoneAlert({ title, message, type, actions, data }) {
    try {
      // If we have a custom in-app notification component, use it
      if (this.inAppNotificationRef && this.inAppNotificationRef.showNotification) {
        this.inAppNotificationRef.showNotification({
          title,
          message,
          onPress: actions[0]?.onPress || (() => {}),
          additionalProps: { type, data }
        });
        return;
      }

      // Fallback to React Native Alert
      const alertButtons = actions.map(action => ({
        text: action.title || action.text || 'OK',
        onPress: action.onPress || (() => {}),
        style: action.style || 'default'
      }));

      // Add default OK button if no actions
      if (alertButtons.length === 0) {
        alertButtons.push({ text: 'OK', style: 'default' });
      }

      Alert.alert(title || 'ChillBaby', message || '', alertButtons);
      sendErrorReport('success', 'phone_alert_shown');
      
    } catch (error) {
      console.error('❌ Failed to show phone alert:', error);
      sendErrorReport(error, 'phone_alert_error');
      throw error;
    }
  }

  /**
   * Show local notification on phone
   */
  showLocalNotification({ title, message, type, data }) {
    try {
      if (Platform.OS === 'ios') {
        PushNotificationIOS.presentLocalNotification({
          alertTitle: title || 'ChillBaby',
          alertBody: message || '',
          userInfo: { type, ...data },
          category: type === 'critical' ? 'CRITICAL_ALERT' : 'DEFAULT'
        });
      } else {
        PushNotification.localNotification({
          channelId: 'chillbaby-notifications',
          title: title || 'ChillBaby',
          message: message || '',
          userInfo: { type, ...data },
          importance: type === 'critical' ? 'high' : 'default',
          priority: type === 'critical' ? 'high' : 'default',
          smallIcon: 'ic_notification',
          largeIcon: 'ic_launcher'
        });
      }
      
      sendErrorReport('success', 'local_notification_sent');
    } catch (error) {
      console.error('❌ Failed to show local notification:', error);
      sendErrorReport(error, 'local_notification_error');
    }
  }

  /**
   * Fallback notification when all else fails
   */
  fallbackNotification({ title, message }) {
    try {
      if (this.appState === 'active') {
        Alert.alert(title || 'ChillBaby', message || 'Notification');
      } else {
        this.showLocalNotification({ title, message, type: 'fallback' });
      }
    } catch (error) {
      console.error('❌ Even fallback notification failed:', error);
    }
  }

  /**
   * Process any pending notifications
   */
  processPendingNotifications() {
    if (this.pendingNotifications.length > 0) {
      const notifications = [...this.pendingNotifications];
      this.pendingNotifications = [];
      
      notifications.forEach(notification => {
        this.showNotification(notification);
      });
    }
  }

  /**
   * Add notification to pending queue
   */
  queueNotification(notification) {
    this.pendingNotifications.push(notification);
  }

  /**
   * Get scenario name for logging
   */
  getScenarioName(carPlayConnected, isForeground) {
    if (carPlayConnected && isForeground) return 'CarPlay+Foreground';
    if (carPlayConnected && !isForeground) return 'CarPlay+Background';
    if (!carPlayConnected && isForeground) return 'Phone+Foreground';
    return 'Phone+Background';
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout);
    }
    this.pendingNotifications = [];
    this.inAppNotificationRef = null;
  }
}

// Export singleton instance
export default new NotificationManager();
