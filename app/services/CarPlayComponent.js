/**
 * CarPlay Component for handling notifications and alerts
 * Integrates with react-native-carplay for CarPlay functionality
 */

import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  NativeEventEmitter,
  NativeModules,
  View,
  AppState,
} from 'react-native';
import { CarPlay, InformationTemplate } from 'react-native-carplay';
import { useSelector } from 'react-redux';
import { sendErrorReport } from '../utils/commonFunction';
import NotificationManager from './NotificationManager';

const { RNCarPlayStatus } = NativeModules;

const CarPlayComponent = () => {
  // Note: navigation prop removed as it's no longer needed with NotificationManager
  const user = useSelector(state => state.auth.userData);
  const { alertData } = useSelector(state => state.bluetooth);

  // state + refs
  const [carPlayConnected, setCarPlayConnected] = useState(false);
  const carPlayConnectedRef = useRef(carPlayConnected);
  const pendingNotification = useRef(null);
  const appStateRef = useRef(AppState.currentState);

  // keep refs in sync to avoid stale closures
  useEffect(() => {
    carPlayConnectedRef.current = carPlayConnected;
    // Update NotificationManager with CarPlay connection status
    NotificationManager.setCarPlayConnected(carPlayConnected);
  }, [carPlayConnected]);

  useEffect(() => {
    const sub = AppState.addEventListener?.('change', next => {
      appStateRef.current = next;
    });
    return () => {
      if (sub && sub.remove) sub.remove();
    };
  }, []);

  // Centralized handler for notifications using NotificationManager
  const handleNotification = useCallback(notification => {
    try {
      sendErrorReport(notification, 'carplay_notification');
      console.log('🚀 ~ CarPlayComponent ~ notification:', notification);

      // Use the new NotificationManager to handle all scenarios
      NotificationManager.showNotification(notification);
    } catch (error) {
      console.error('❌ Error handling CarPlay notification:', error);
      sendErrorReport(error, 'carplay_notification_error');
    }
  }, []);

  // Sync initial CarPlay connection state from native module and subscribe to events
  useEffect(() => {
    let nativeEmitter;
    let connectionListener = null;

    async function initCarPlayState() {
      try {
        if (RNCarPlayStatus && RNCarPlayStatus.isCarPlayConnected) {
          const connected = await RNCarPlayStatus.isCarPlayConnected();
          setCarPlayConnected(!!connected);
          carPlayConnectedRef.current = !!connected;

          // If CarPlay is connected and we have a pending notification queued earlier, present it
          if (connected && pendingNotification.current) {
            // small timeout to let state settle
            setTimeout(() => {
              handleNotification(pendingNotification.current);
              pendingNotification.current = null;
            }, 50);
          }
        } else {
          // Fallback: use CarPlay.connected if RNCarPlayStatus isn't available
          setCarPlayConnected(!!CarPlay.connected);
          carPlayConnectedRef.current = !!CarPlay.connected;
        }
      } catch (e) {
        console.warn('Failed to read RNCarPlayStatus', e);
        setCarPlayConnected(!!CarPlay.connected);
        carPlayConnectedRef.current = !!CarPlay.connected;
      }

      // subscribe to native events if emitter exists
      if (RNCarPlayStatus && RNCarPlayStatus.addListener) {
        nativeEmitter = new NativeEventEmitter(RNCarPlayStatus);
        connectionListener = nativeEmitter.addListener(
          'CarPlayConnectionChanged',
          event => {
            const connected = !!event?.connected;
            sendErrorReport(connected, 'carplay_native_event');
            setCarPlayConnected(connected);
            carPlayConnectedRef.current = connected;

            // if connected and we had pending notification, show it on CarPlay
            if (connected && pendingNotification.current) {
              setTimeout(() => {
                handleNotification(pendingNotification.current);
                pendingNotification.current = null;
              }, 50);
            }
          },
        );
      } else {
        // fallback to using react-native-carplay callbacks
        CarPlay.registerOnConnect(() => {
          setCarPlayConnected(true);
          carPlayConnectedRef.current = true;
          if (pendingNotification.current) {
            setTimeout(() => {
              handleNotification(pendingNotification.current);
              pendingNotification.current = null;
            }, 50);
          }
        });
        CarPlay.registerOnDisconnect(() => {
          setCarPlayConnected(false);
          carPlayConnectedRef.current = false;
        });
      }
    }

    initCarPlayState();

    return () => {
      if (connectionListener) connectionListener.remove();
      if (nativeEmitter && nativeEmitter.removeAllListeners) {
        nativeEmitter.removeAllListeners('CarPlayConnectionChanged');
      }
      // unregister CarPlay callbacks if used fallback
      try {
        CarPlay.unregisterOnConnect && CarPlay.unregisterOnConnect();
        CarPlay.unregisterOnDisconnect && CarPlay.unregisterOnDisconnect();
      } catch (e) {}
    };
    // run once on mount
  }, [handleNotification]);

  // Listen for alertData changes from redux and route to handler
  useEffect(() => {
    if (alertData && alertData.message) {
      // If CarPlay is connected, handleNotification will present on CarPlay.
      // If not connected, the handler decides in-app vs local-notification.
      // We pass along actions and callback if present.
      const notificationPayload = {
        title: alertData?.title || 'Alert',
        message: alertData?.message,
        type: 'alert',
        actions: alertData?.actions || [],
        onActionPressed: alertData?.onActionPressed, // optional callback
      };

      // If CarPlay is not ready, but we want to queue for CarPlay specifically, set pendingNotification.
      // In this implementation we show in-app if foreground, or send local notification if background.
      // If you prefer to always queue and present on CarPlay when it connects, uncomment the following:
      // if (!carPlayConnectedRef.current) { pendingNotification.current = notificationPayload; return; }

      handleNotification(notificationPayload);
    }
  }, [alertData, handleNotification]);

  // Set up CarPlay main template (info) - update when connection or user changes
  useEffect(() => {
    try {
      const infoTemplate = new InformationTemplate({
        title: `ChillBaby${user?.full_name ? ` - ${user.full_name}` : ''}`,
        subtitle: 'Your personalized dashboard in CarPlay',
        actions: [],
        items: [
          {
            title: 'Welcome',
            detail: user?.full_name || 'Guest User',
          },
          {
            title: 'Status',
            detail: carPlayConnected
              ? '✅ Connected to CarPlay'
              : '❌ Not Connected',
          },
        ],
      });

      // Only set root if CarPlay is connected; setting root when not connected is a no-op in many libs,
      // but we guard to be safe.
      if (carPlayConnected) {
        CarPlay.setRootTemplate(infoTemplate);
      }
    } catch (e) {
      console.warn('Failed to set CarPlay root template', e);
    }
  }, [carPlayConnected, user]);

  // Ensure we also register RNCarPlay library connect/disconnect events to keep parity
  useEffect(() => {
    const onConnect = () => {
      sendErrorReport('true', 'carplay_connected_onconnect_js');
      setTimeout(() => {
        setCarPlayConnected(true);
        carPlayConnectedRef.current = true;
        if (pendingNotification.current) {
          handleNotification(pendingNotification.current);
          pendingNotification.current = null;
        }
      }, 50);
    };

    const onDisconnect = () => {
      sendErrorReport('false', 'carplay_connected_onDisconnect_js');
      setCarPlayConnected(false);
      carPlayConnectedRef.current = false;
    };

    // These register/unregister methods often exist on the library; guard them
    try {
      CarPlay.registerOnConnect && CarPlay.registerOnConnect(onConnect);
      CarPlay.registerOnDisconnect &&
        CarPlay.registerOnDisconnect(onDisconnect);
    } catch (e) {
      // ignore
    }

    return () => {
      try {
        CarPlay.unregisterOnConnect && CarPlay.unregisterOnConnect(onConnect);
        CarPlay.unregisterOnDisconnect &&
          CarPlay.unregisterOnDisconnect(onDisconnect);
      } catch (e) {}
    };
  }, [handleNotification]);

  // don't render anything UI-wise for CarPlay screen in the phone app
  return <View />;
};

export default CarPlayComponent;
