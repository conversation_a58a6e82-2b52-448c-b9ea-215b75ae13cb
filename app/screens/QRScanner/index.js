/* eslint-disable no-nested-ternary */
/* eslint-disable eqeqeq */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
/* eslint-disable no-fallthrough */
/* eslint-disable max-len */
/* eslint-disable no-console */
/* eslint-disable quotes */
/**
 * Sample BLE React Native App
 *
 * @format
 * @flow strict-local
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  NativeModules,
  NativeEventEmitter,
  Platform,
  PermissionsAndroid,
  BackHandler,
  Text,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
  ToastAndroid,
} from 'react-native';
import QRCodeScanner from 'react-native-qrcode-scanner';
import _, {
  find,
  findIndex,
  isArray,
  isBoolean,
  isEmpty,
  isObject,
  isUndefined,
} from 'lodash';
import BleManager from 'react-native-ble-manager';
import BluetoothStateManager from 'react-native-bluetooth-state-manager';
import { useDispatch, useSelector } from 'react-redux';
import Toast from 'react-native-simple-toast';
import {
  openSettings,
  check,
  PERMISSIONS,
  requestMultiple,
  request,
} from 'react-native-permissions';
import _BackgroundTimer from 'react-native-background-timer';
import { Button } from 'react-native-share';

import { useIsFocused } from '@react-navigation/native';
import { CustomIcon } from '../../config/LoadIcons';
import styles from './styles';
import CHeader from '../../components/CHeader';
import GradientBack from '../../components/gradientBack';
import { translate } from '../../lang/Translate';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import { sendErrorReport } from '../../utils/commonFunction';
import BaseColor from '../../config/colors';
import { FontFamily } from '../../config/typography';
import StepPopup from '../../components/StepPopup';
import AuthAction from '../../redux/reducers/auth/actions';

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

/**
 *
 *@module QRScanner
 *
 */
const QRScanner = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const fromChildInfo = route?.params?.fromChildInfo;
  // console.log('from child----2', fromChildInfo);
  const childObj = route?.params?.childObj;
  const child_id = route?.params?.child_id;
  const token = useSelector(state => state.auth.accessToken);
  const languageData = useSelector(state => state.language);
  const [isScanning, setIsScanning] = useState(false);
  const peripherals = new Map();
  const [list, setList] = useState([]);
  const connectedDeviceDetail = useSelector(
    state => state.bluetooth.connectedDeviceDetail,
  );
  const [notFound, setNotFound] = useState(0);

  const {
    isClickAddQr,
    bleDeviceList,
    isSkipShow,
    isBleConnected,
    deletedRecent,
  } = useSelector(state => state.bluetooth);
  const {
    setStep4Done,
    setStep4bDone,
    setStep5Done,
    setStep5bDone,
    setStep5cDone,
    setStep5dDone,
    setStep5eDone,
    setStep5fDone,
    setStep6Done,
    setStep7,
    setStep8Done,
  } = AuthAction;
  const step4Done = useSelector(state => state.auth.step4Done);
  const step4bDone = useSelector(state => state.auth.step4bDone);
  const step8Done = useSelector(state => state.auth.step8Done);
  const closeOnboarding = useSelector(state => state.auth.closeOnboarding);
  const [isRefreshing, setisRefreshing] = useState(false);
  const [refresh, setrefresh] = useState(false);
  const [deviceId, setDeviceId] = useState(null);
  const [skip, setSkip] = useState(false);
  const [scanningText, setScanningText] = useState(
    translate('searchingQRCode'),
  );
  const [showStep4, setShowStep4] = useState(false);
  const [showStep4b, setShowStep4b] = useState(false);
  const userData = useSelector(state => state.auth.userData);
  let scanner = useRef(null);
  // const [bluetoothStatus, setbluetoothStatus] = useState(false);
  const isFocused = useIsFocused();
  const triedI = 0;
  const curInt = null;

  // useEffect(() => {
  //   clearInterval(triedI);
  // }, []);

  // useEffect(() => {
  //   if (triedI < 6) {
  //     curInt = setInterval(() => {
  //       if (triedI > 6 && !isBleConnected) {
  //         triedI = 6;
  //         setSkip(true);
  //       } else if (isBleConnected) {
  //         setSkip(false);
  //         clearInterval(curInt);
  //         triedI = 0;
  //       } else {
  //         triedI += 1;
  //       }
  //     }, 1000);
  //   }
  //   return () => {
  //     setSkip(false);
  //     triedI = 0;
  //   };
  // }, [triedI]);
  useEffect(() => {
    async function initBleManager() {
      if (Platform.OS === 'android') {
        await BleManager.start({ showAlert: true, forceLegacy: true });
        console.log('Módulo inicializado');
      }

      if (Platform.OS === 'android' && Platform.Version >= 31) {
        sendErrorReport(Platform.Version, 'platform version4');
        const bluetoothConnectResult = await check(
          PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
        );
        console.log('BLUETOOTH_CONNECT--q-', bluetoothConnectResult);

        if (bluetoothConnectResult === 'granted') {
          // setBPermission(true);
        }

        const bluetoothConnectStatus = await request(
          PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
        );
        console.log('BLUETOOTH_CONNECT----1', bluetoothConnectStatus);

        const bluetoothConnectStatuses = await request(
          PERMISSIONS.ANDROID.BLUETOOTH_CONNECT,
        );
        console.log(
          'BLUETOOTH_CONNECT--2',
          bluetoothConnectStatuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
        );
      }
    }

    initBleManager();
  }, []);

  useEffect(() => {
    // if (Platform.OS == "android") {
    setTimeout(() => {
      setSkip(true);
    }, 6000);
    // }
  }, []);

  useEffect(() => {
    setTimeout(() => {
      if (!step4Done && !closeOnboarding && isEmpty(connectedDeviceDetail)) {
        sendErrorReport('step4', 'step4_qrscanner');
        setShowStep4(true); // m
      } else if (
        step4Done &&
        !step4bDone &&
        !closeOnboarding &&
        isEmpty(connectedDeviceDetail)
      ) {
        sendErrorReport('step4b', 'step4b_qrscanner');
        setShowStep4b(true); // m
      }
      setTimeout(() => {
        if (!closeOnboarding && !step8Done && isEmpty(connectedDeviceDetail)) {
          sendErrorReport('step4', 'step4_qrscanner2');
          setShowStep4(true); // m
          dispatch(setStep4Done(false));
          dispatch(setStep4bDone(false));
          dispatch(setStep5Done(false));
          dispatch(setStep5bDone(false));
          dispatch(setStep5cDone(false));
          dispatch(setStep5dDone(false));
          dispatch(setStep5eDone(false));
          dispatch(setStep5fDone(false));
          dispatch(setStep6Done(false));
          dispatch(setStep7(false));
          dispatch(setStep8Done(false));
        }
      }, 300);
    }, 500);
  }, []);
  const startScan = async () => {
    if (!isScanning) {
      await BleManager.scan([], 3, true)
        .then(results => {
          setIsScanning(true);
          setrefresh(false);
        })
        .catch(err => {
          setrefresh(false);
          console.error(err);
          sendErrorReport(err, 'scan_error');
        });
    }
  };

  useEffect(() => {
    setTimeout(() => {
      startScan();
    }, 1500);
  }, []);

  // useEffect(() => {
  //   dispatch(BluetoothAction.setBleDeviceList(list));
  // }, [list]);

  const handleStopScan = () => {
    setIsScanning(false);
    setisRefreshing(false);
    dispatch(BluetoothAction.setClickAddQr(false));
  };

  const handleDiscoverPeripheral = peripheral => {
    if (peripheral.name) {
      peripherals.set(peripheral.id, peripheral);
      setList(Array.from(peripherals.values()));
    }
  };

  BluetoothStateManager.onStateChange(bluetoothState => {
    // do something...
    switch (bluetoothState) {
      case 'Unknown':
      case 'Resetting':
      case 'Unsupported':
      case 'Unauthorized':
      case 'PoweredOff':
        Toast.show(translate('turnOnBle'));
      // Toast.show("Please turn on your bluetooth");
      case 'PoweredOn':
        // startScan();
        break;
      default:
        break;
    }
  }, true /*= emitCurrentState */);

  useEffect(() => {
    BluetoothStateManager.getState().then(bluetoothState => {
      switch (bluetoothState) {
        case 'Unknown':
        case 'Resetting':
        case 'Unsupported':
        case 'Unauthorized':
        case 'PoweredOff':
          // Toast.show("Please turn on your bluetooth");
          Toast.show(translate('turnOnBle'));
          if (Platform.OS == 'android') {
            sendErrorReport(true, 'requestToEnableQR');
            check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then(res => {
              console.log('BLUETOOTH_CONNECT---', res);
              if (res === 'granted') {
                BluetoothStateManager.requestToEnable().then(result => {
                  console.log(
                    'BluetoothStateManager.requestToEnable -> result',
                    result,
                  );
                });
              }
            });
            request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
              .then(result => {
                console.log('BLUETOOTH_CONNECT----1', result);
              })
              .then(statuses => {
                console.log(
                  'BLUETOOTH_CONNECT--2',
                  statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
                );
              });
          } else {
            console.log('=====555555');
            BluetoothStateManager.openSettings();
          }
          break;
        case 'PoweredOn':
          // startScan();
          break;
        default:
          break;
      }
    });
  }, []);

  useEffect(() => {
    /* Listening to IOS Background events as per the docs - Not Tested */
    let discoverPeripheralListener,
      stopScanListener,
      centralManagerRestoreState;
    if (isClickAddQr || isRefreshing) {
      centralManagerRestoreState = bleManagerEmitter.addListener(
        'BleManagerCentralManagerWillRestoreState',
        data => {
          console.log(
            'BLE ==> BleManagerCentralManagerWillRestoreState ===> ',
            data,
          );
        },
      );

      discoverPeripheralListener = bleManagerEmitter.addListener(
        'BleManagerDiscoverPeripheral',
        handleDiscoverPeripheral,
      );
      stopScanListener = bleManagerEmitter.addListener(
        'BleManagerStopScan',
        handleStopScan,
      );

      if (Platform.OS === 'android' && Platform.Version >= 23) {
        console.log('called---7');
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ).then(result => {
          if (result) {
            console.log('Permission is OK');
          } else {
            PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            ).then(res => {
              if (res) {
                console.log('User accept');
              } else {
                console.log('User refuse');
              }
            });
          }
        });
      }
    }

    return () => {
      // console.log("unmount");
      // dispatch(BluetoothAction.setBleDeviceList(list));
      // bleManagerEmitter.removeListener(
      //   'BleManagerDiscoverPeripheral',
      //   handleDiscoverPeripheral,
      // );
      // bleManagerEmitter.removeListener('BleManagerStopScan', handleStopScan);
      // bleManagerEmitter.removeListener(
      //   'BleManagerDisconnectPeripheral',
      //   handleDisconnectedPeripheral
      // );
      // bleManagerEmitter.removeListener(
      //   'BleManagerDidUpdateValueForCharacteristic',
      //   handleUpdateValueForCharacteristic
      // );

      discoverPeripheralListener?.remove();
      stopScanListener?.remove();
      centralManagerRestoreState?.remove();
    };
  }, [isClickAddQr, isRefreshing]);

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  /** this function for onReadQR
   * @function onReadQR
   * @param {object} data device_bluetooth_name
   */
  const onReadQR = async e => {
    setScanningText(translate('readingQRCode'));
    // list.map((d) => console.log("QR Available Item", d.name));
    sendErrorReport(e, 'Scan_data');
    console.log('🚀 ~ file: index.js ~ line 254 ~ onReadQR ~ e', e);
    if (e.data === '') {
      Toast.show('Invalid QR Code.Please try again.');
      return;
    }
    // Scan QR Code and Find SSID
    let qrSSID = '';
    if (e?.data && _.includes(e.data, 'http://')) {
      const readD = _.split(e.data, 'http://');
      const readD1 = _.split(readD[1], ',');
      qrSSID = readD1.splice(0, 1).toString();
    } else {
      const readD = _.split(e.data, ',');
      const d = readD.splice(0, 1).toString();
      if (d) {
        qrSSID = d.replace(' ', '0');
      }
    }
    // let data;
    // if (qrSSID) {
    sendErrorReport(list, 'Device_List');
    // console.log('list=====', list);
    let data = find(
      list,
      // (lt) => lt?.name.toLowerCase() === e?.data.toLowerCase()
      lt => lt?.name.toLowerCase() === qrSSID.toLowerCase(),
    );

    const testUser =
      !isEmpty(userData) && userData.email === '<EMAIL>';

    console.log('🚀 ~ onReadQR ~ testUser is Groovy-->>:', testUser);
    console.log('🚀 ~ onReadQR ~ data Groovy-->>:', data, 'QRSSID', qrSSID);
    console.log('data=====', data);
    // sendErrorReport(data, 'Data_QrScan');
    // sendErrorReport(qrSSID, 'qrSSID');
    // }
    if (isFocused) {
      // console.log("🚀 ~ file: index.js ~ line 264 ~ onReadQR ~ SSID", SSID);
      if (!data) {
        //! ----Check if Already Connected to same device in that case
        await BleManager.getConnectedPeripherals([]).then(peripheralsArray => {
          if (!isEmpty(peripheralsArray)) {
            const isAlreadyConnected = find(
              peripheralsArray,
              lt => lt?.name.toLowerCase() === qrSSID.toLowerCase(),
            );
            if (isAlreadyConnected) {
              console.log(
                '🚀 ~ BleManager.getConnectedPeripherals ~ isAlreadyConnected:',
                isAlreadyConnected,
              );

              data = isAlreadyConnected;
            }
          }
        });
      }

      const headers = {
        'Content-Type': 'application/json',
        authorization: token ? `Bearer ${token}` : '',
      };

      try {
        const response = await getApiData(
          BaseSetting.endpoints.getDevice,
          'POST',
          {
            // device_bluetooth_name: e?.data,
            // device_ssid: SSID,
            device_ssid: testUser ? `${qrSSID}AVDF` : qrSSID,
            product_id: data?.id,
            lang_code: languageData?.languageData || 'es',
          },
          headers,
        );
        const dd = {
          device_ssid: `${qrSSID}`,
          product_id: data?.id,
          lang_code: languageData?.languageData || 'es',
        };
        console.log('param ------>', dd);
        // sendErrorReport(response, 'response_response');
        console.log('response=====', response);
        if (response.success && !isEmpty(response.data)) {
          // sendErrorReport(true, 'deviceIDset15');
          setDeviceId(response?.data?.id);
          if (isArray(connectedDeviceDetail)) {
            const obj = { ...response?.data };
            obj.product_id = data?.id;

            const arr = [...connectedDeviceDetail] || [];
            const index = findIndex(
              arr,
              lt => lt?.product_id === obj?.product_id,
            );
            if (index > -1) {
              arr[index] = obj;
            } else {
              arr.unshift(obj);
            }
            dispatch(BluetoothAction.setConnectedDeviceDetail(arr));
            dispatch(BluetoothAction.setConnectedDeviceDetails(arr));
            dispatch(BluetoothAction.setSwiperKey(obj?.product_id));
            dispatch(BluetoothAction.setActiveDeviceId(obj));
            // dispatch(BluetoothAction.setConnectedDeviceDetails(...connectedDeviceDetails, obj));
          }
          // dispatch(BluetoothAction.setConnectedDeviceDetail(deviceArr));
          // dispatch(BluetoothAction.setConnectedDeviceId(response?.data?.id));
          // dispatch(BluetoothAction.setConnectedDeviceName(e.data));
          if (isObject(data) && !isEmpty(data)) {
            dispatch(BluetoothAction.setDeviceID(''));
            dispatch(BluetoothAction.setIsFromScanner(true));
            setTimeout(() => {
              sendErrorReport(true, 'deviceIdSet11');
              dispatch(BluetoothAction.setDeviceID(data.id));
              dispatch(BluetoothAction.setLastDeviceId(data.id));
              dispatch(BluetoothAction.setIsConnectLoad(true));
              navigation.navigate('Connect', {
                product_id: data?.id,
                device_id: response?.data?.id || deviceId,
                device_data: e?.data,
                device_ssid: qrSSID,
                fromChildInfo,
                childObj,
                child_id,
              });
            }, 2500);
          } else {
            setIsScanning(false);
            // Toast.show("Can't find any device. Please try again");
            // Toast.show(translate("cannotFindDevice"));
            setNotFound(notFound + 1);
            console.log('jhmore than two', notFound);
            sendErrorReport(notFound.toString(), 'notFound_err');

            if (Platform.OS === 'android') {
              ToastAndroid.show(
                translate('cannotFindDevice'),
                ToastAndroid.LONG,
              );
            } else {
              Toast.show(translate('cannotFindDevice'));
            }

            if (notFound === 1) {
              setNotFound(0);
              console.log('jhmore than two');
              setTimeout(() => {
                navigation.navigate('bleList', {
                  fromChildInfo,
                  childObj,
                  child_id,
                });
              }, 4000);
            }
            onRefresh();
          }
        } else {
          console.log('djhhjhjd---', response.message);
          // Toast.show(response.message);
          setNotFound(notFound + 1);
          console.log('jhmore than two', notFound);
          sendErrorReport(notFound.toString(), 'notFound_err');

          if (Platform.OS === 'android') {
            ToastAndroid.show(response.message, ToastAndroid.LONG);
          } else {
            Toast.show(response.message);
          }

          if (notFound === 1) {
            setNotFound(0);
            setTimeout(() => {
              navigation.navigate('bleList', {
                fromChildInfo,
                childObj,
                child_id,
              });
            }, 4000);
          }
          setScanningText(translate('searchingQRCode'));
          sendErrorReport(response.message, 'on_read_qr_error');
        }
      } catch (error) {
        sendErrorReport(error, 'on_read_qr');
      }
    } else {
      sendErrorReport(qrSSID, 'qrSSIDElse');
    }
  };

  // const onReadQRList = async (item) => {
  //   sendErrorReport(item, "item__item");
  //   const headers = {
  //     "Content-Type": "application/json",
  //     authorization: token ? `Bearer ${token}` : "",
  //   };

  //   try {
  //     const response = await getApiData(
  //       BaseSetting.endpoints.getDevice,
  //       "POST",
  //       {
  //         device_ssid: item?.name,
  //         product_id: item?.id,
  //         lang_code: languageData?.languageData || "es",
  //       },
  //       headers
  //     );

  //     if (response.success && !isEmpty(response.data)) {
  //       setDeviceId(response?.data?.id);
  //       if (isArray(connectedDeviceDetail)) {
  //         const obj = { ...response?.data };
  //         obj.product_id = item?.id;

  //         const arr = [...connectedDeviceDetail] || [];
  //         const index = findIndex(
  //           arr,
  //           (lt) => lt?.product_id === obj?.product_id
  //         );
  //         if (index > -1) {
  //           arr[index] = obj;
  //         } else {
  //           arr.unshift(obj);
  //         }
  //         dispatch(BluetoothAction.setConnectedDeviceDetail(arr));
  //         dispatch(BluetoothAction.setConnectedDeviceDetails(arr));
  //         dispatch(BluetoothAction.setSwiperKey(obj?.product_id));
  //         dispatch(BluetoothAction.setActiveDeviceId(obj));
  //         // dispatch(BluetoothAction.setConnectedDeviceDetails(...connectedDeviceDetails, obj));
  //       }
  //       // dispatch(BluetoothAction.setConnectedDeviceDetail(deviceArr));
  //       // dispatch(BluetoothAction.setConnectedDeviceId(response?.data?.id));
  //       // dispatch(BluetoothAction.setConnectedDeviceName(e.data));
  //       if (isObject(item) && !isEmpty(item)) {
  //         dispatch(BluetoothAction.setDeviceID(""));
  //         setTimeout(() => {
  //           dispatch(BluetoothAction.setDeviceID(item.id));
  //           dispatch(BluetoothAction.setLastDeviceId(item.id));
  //           dispatch(BluetoothAction.setIsConnectLoad(true));
  //           dispatch(BluetoothAction.setClickAddQr(false));
  //           navigation.navigate("Connect", {
  //             product_id: item?.id,
  //             device_id: response?.data?.id || deviceId,
  //             device_data: item?.name,
  //             device_ssid: item?.name,
  //           });
  //         }, 2500);
  //       } else {
  //         setIsScanning(false);
  //         // Toast.show("Can't find any device. Please try again");
  //         Toast.show(translate("cannotFindDevice"));
  //         startScan();
  //       }
  //     } else {
  //       Toast.show(response.message);
  //       setScanningText(translate("searchingQRCode"));
  //     }
  //   } catch (error) {
  //     console.log("error device detail ===", error);
  //     sendErrorReport(error, "on_read_qr");
  //   }
  // };

  const NoPermissionViewIos = (
    <View
      style={{
        width: Dimensions.get('window').width * 0.8,
        alignSelf: 'center',
      }}>
      <Text style={styles.qrTextStyle}>{translate('noCameraAcces')}</Text>
      <TouchableOpacity onPress={() => openSettings()}>
        <Text style={styles.openSettingsText}>{translate('openSettings')}</Text>
      </TouchableOpacity>
    </View>
  );

  // const renderItem = ({ item }) => (
  //   // console.log("renderItem -> item", list);
  //   <View
  //     style={{
  //       padding: 12,
  //       borderRadius: 8,
  //       backgroundColor: "#fff",
  //       shadowColor: "#000",
  //       margin: 8,
  //       sshadowColor: "#000",
  //       shadowOffset: {
  //         width: 0,
  //         height: 2,
  //       },
  //       shadowOpacity: 0.25,
  //       shadowRadius: 3.84,
  //       elevation: 5,
  //     }}
  //   >
  //     <View
  //       style={{
  //         flexDirection: "row",
  //         alignItems: "center",
  //         marginBottom: 8,
  //       }}
  //     >
  //       {/* <Text style={{ fontSize: 18, fontWeight: "700" }}>NAME : </Text> */}
  //       <Text
  //         style={{
  //           fontSize: 18,
  //           marginStart: 8,
  //           flex: 1,
  //           fontWeight: "700",
  //         }}
  //       >
  //         {item?.name == "NO NAME" ? "N/A" : item?.name}
  //       </Text>
  //       <TouchableOpacity
  //         style={{
  //           backgroundColor:
  //             item?.advertising?.isConnectable == 1 ? "green" : "green",
  //           padding: 8,
  //           borderRadius: 8,
  //         }}
  //         activeOpacity={0.7}
  //         onPress={() => {
  //           onReadQRList(item);
  //         }}
  //       >
  //         <Text style={{ color: "#fff" }}>CONNECT</Text>
  //       </TouchableOpacity>
  //     </View>
  //     <View style={{ flexDirection: "row" }}>
  //       <Text style={{ fontSize: 15, marginStart: 8, flex: 1 }}>
  //         {item?.id}
  //       </Text>
  //     </View>
  //   </View>
  // );

  const onRefresh = async () => {
    setisRefreshing(true);
    await BleManager.stopScan().then(() => {
      // Success code
      console.log('Scan stopped qr scanner');
    });
    setTimeout(() => {
      startScan();
    }, 1000);
  };

  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <CHeader
        title={translate('camera')}
        // backBtn
        leftIconName="left-arrow"
        onLeftPress={() => {
          dispatch(BluetoothAction.setClickAddQr(false));
          navigation.goBack();
        }}
      />
      <View style={{ flex: 1 }}>
        <View style={{ alignItems: 'center' }}>
          <ActivityIndicator size={22} color={BaseColor.blackColor} />
          <Text style={[styles.qrTextStyle, { paddingTop: 10 }]}>
            {scanningText}
          </Text>
          <Text style={[styles.qrTextStyle, { paddingTop: 10, fontSize: 12 }]}>
            {translate('maintainPressure')}
          </Text>
        </View>
        {
          <QRCodeScanner
            // showMarker
            ref={node => {
              scanner = node;
            }}
            onRead={onReadQR}
            reactivate
            reactivateTimeout={5000}
            cameraProps={{ height: Dimensions.get('window').height }}
            notAuthorizedView={NoPermissionViewIos}
          />
        }
        <CustomIcon
          name="scan-"
          size={
            skip
              ? Dimensions.get('window').height / 2 - 80
              : Dimensions.get('window').height / 2 - 50
          }
          color={BaseColor.blackColor}
          style={{
            position: 'absolute',
            justifyContent: 'center',
            alignContent: 'center',
            alignSelf: 'center',
            bottom: Platform.OS === 'android' ? 32 : 45,
          }}
        />
      </View>
      {/* )} */}
      <View style={styles.bottomViewStyle}>
        <View style={styles.scanDescViewStyle}>
          <Text style={styles.qrTextStyle}>{translate('scanQRText')}</Text>
          {skip && !isSkipShow && (
            <TouchableOpacity
              style={{
                marginTop: 16,
                alignItems: 'center',
                backgroundColor: BaseColor.whiteColor,
              }}
              activeOpacity={0.7}
              // onPress={() => dispatch(BluetoothAction.setSkipShow(true))}
              onPress={() =>
                navigation.navigate('bleList', {
                  fromChildInfo,
                  childObj,
                  child_id,
                })
              }>
              <Text
                style={{
                  color: BaseColor.whiteColor,
                  textAlign: 'center',
                  backgroundColor: BaseColor.alertRed,
                  width: '40%',
                  padding: 10,
                  borderWidth: 0.5,
                  borderRadius: 20,
                  borderColor: BaseColor.alertRed,
                }}>
                {translate('connectBtn')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
      <StepPopup
        visible={showStep4}
        descriptionText={translate('step4')}
        image={require('../../assets/images/step4.jpg')}
        onNext={() => {
          setShowStep4(false);
          dispatch(setStep4Done(true));
          setShowStep4b(true);
          setTimeout(() => {
            setShowStep4b(false);
            dispatch(setStep4bDone(true));
          }, 9000);
        }}
      />
      <StepPopup
        visible={showStep4b}
        descriptionText="step4b1"
        image={require('../../assets/images/step4.jpg')}
        onNext={() => {
          dispatch(setStep4bDone(true));
          setShowStep4b(false);
        }}
      />
    </View>
  );
};

export default QRScanner;
