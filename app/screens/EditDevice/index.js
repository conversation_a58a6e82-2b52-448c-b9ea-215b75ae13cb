/* eslint-disable quotes */
import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  Image,
  StatusBar,
  SafeAreaView,
  ScrollView,
  Modal,
  ActivityIndicator,
  BackHandler,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import { useDispatch, useSelector } from 'react-redux';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { useTheme } from '@react-navigation/native';
import { isEmpty, isObject } from 'lodash';
import CButton from '../../components/CButton';
import GradientBack from '../../components/gradientBack';
import styles from './styles';
import CHeader from '../../components/CHeader';
import DropDown from '../../components/DropDown';
import { translate } from '../../lang/Translate';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import { sendErrorReport } from '../../utils/commonFunction';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';

/**
 *
 *@module EditDevice
 *
 */
const EditDevice = ({ navigation, route }) => {
  const {
    params: { deviceDetail },
  } = route;
  const dispatch = useDispatch();
  const colors = useTheme();
  const BaseColor = colors.colors;

  const imageAnim = useSharedValue(0);
  const infoAnim = useSharedValue(0);
  const opacityAnim = useSharedValue(0);
  const [btnLoad, setBtnLoad] = useState('');

  const imageAnimationStyle = useAnimatedStyle(() => ({
    transform: [
      {
        scale: withTiming(imageAnim.value, {
          duration: 1000,
        }),
      },
    ],
  }));

  const infoStyleAnim = useAnimatedStyle(() => ({
    transform: [
      {
        translateY: withTiming(infoAnim.value, {
          duration: 1000,
        }),
      },
    ],
    opacity: withTiming(opacityAnim.value, {
      duration: 1000,
    }),
  }));

  useEffect(() => {
    imageAnim.value = 1;
    opacityAnim.value = 1;
    infoAnim.value = -200;
  }, []);

  const token = useSelector(state => state.auth.accessToken);

  const [state, setstate] = useState({
    isImage: '',
    nickName: '',
    height: '',
    weight: '',
    imageBase64: '',
    dob: '',
  });

  const [childArr, setchildArr] = useState([]);

  const [loader, setloader] = useState(false);

  // const Validation = () => {
  //   enableAnimateInEaseOut();
  //   if (isEmpty(state.nickName)) {
  //     allErrorFalse();
  //     setNickNameError(true);
  //     setNickNameErrorTxt('Please enter Nickname');
  //   } else if (isEmpty(state.dob)) {
  //     allErrorFalse();
  //     setDobError(true);
  //     setDobErrorTxt('Please Select DOB');
  //   } else if (isEmpty(state.height)) {
  //     allErrorFalse();
  //     Toast.show('Please select Height', Toast.SHORT);
  //   } else if (isEmpty(state.weight)) {
  //     allErrorFalse();
  //     Toast.show('Please select Weight', Toast.SHORT);
  //   } else if (isEmpty(state.isImage)) {
  //     allErrorFalse();
  //     Toast.show('Please Add Profile Picture', Toast.SHORT);
  //   } else {
  //     allErrorFalse();
  //     // childProfile();
  //   }
  // };

  function handleBackButtonClick() {
    navigation.goBack();
    return true;
  }

  useEffect(() => {
    getChildInfo();
  }, []);

  /** This function for get child info
   * @function getChildInfo
   * @param {object} data {}
   */
  const getChildInfo = () => {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    getApiData(
      BaseSetting.endpoints.getUserChild,
      'POST',
      {
        platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      },
      headers,
    )
      .then(response => {
        if (response.success) {
          console.log('RESPONCE===>>>', response);
          setchildArr(response.data);

          const selectedChild = response.data.find(
            item => item.id === deviceDetail.child_id,
          );
          if (isObject(selectedChild) && !isEmpty(selectedChild)) {
            setstate({ ...state, height: selectedChild });
          }
        } else {
          Toast.show(response.message);
        }
      })
      .catch(err => {
        // console.log("ERRR", err);
        sendErrorReport(err, 'get_child_edit_devices');
        // Toast.show('Something went wrong while getting child details');
      });
  };

  // this function for edit child device
  /** this function for edit child device
   * @function editDevice
   * @param {object} data child_id, device_id, new_child_id
   */
  async function editDevice() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    const data = {
      child_id: deviceDetail.child_id,
      device_id: deviceDetail.device_id,
      product_id: deviceDetail.product_id,
      new_child_id: state?.height?.id,
    };

    setBtnLoad('edit');
    if (state.height.id) {
      try {
        const response = await getApiData(
          BaseSetting.endpoints.editDeviceChild,
          'POST',
          data,
          headers,
        );

        Toast.show(response.message);
        setBtnLoad('');
        navigation.navigate('DrawerNav');
      } catch (error) {
        console.log('error ===', error);
        sendErrorReport(error, 'edit_device');
      }
    }
  }

  // this function for delete device
  /** this function for delete device
   * @function deleteDevice
   * @param {object} data child_id, device_id
   */
  async function deleteDevice() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    const data = {
      child_id: deviceDetail.child_id,
      device_id: deviceDetail.device_id,
      product_id: deviceDetail.product_id,
    };

    setBtnLoad('delete');
    if (state.height.id) {
      try {
        const response = await getApiData(
          BaseSetting.endpoints.deleteChildDevice,
          'POST',
          data,
          headers,
        );

        Toast.show(response.message);
        setBtnLoad('');
        sendErrorReport(true, 'deviceIdSet5');
        dispatch(BluetoothAction.setDeviceID(''));
        dispatch(BluetoothAction.setDeletedRecent(true));
        dispatch(BluetoothAction.setActiveDeviceId({}));
        dispatch(BluetoothAction.setConnectedDeviceDetails([]));
        navigation.navigate('DrawerNav');
      } catch (error) {
        console.log('error ===', error);
        sendErrorReport(error, 'delete_device');
      }
    }
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={{ flex: 1, backgroundColor: BaseColor.whiteColor }}>
      {/* <GradientBack /> */}
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : null}>
        <CHeader
          title={
            deviceDetail.device_bluetooth_name === 'Baby_Auto1'
              ? 'Linko'
              : deviceDetail.device_name || ''
          }
          // title="Linko"
          backBtn
          leftIconName
          onLeftPress={() => {
            navigation.goBack();
          }}
        />
        <ScrollView
          contentContainerStyle={[
            {
              flexGrow: 1,
            },
          ]}
          bounces={false}
          showsVerticalScrollIndicator={false}>
          <SafeAreaView style={[styles.root]}>
            <StatusBar backgroundColor="transparent" barStyle="dark-content" />
            <Animated.View
              style={{ paddingBottom: 24, ...imageAnimationStyle }}>
              <Text
                style={[styles.chooseProfile, { color: BaseColor.whiteColor }]}>
                {translate('chooseProfileText')}
              </Text>
              <View
                style={[
                  styles.imageLastShadow,
                  { backgroundColor: BaseColor.card2 },
                ]}>
                <View
                  style={[
                    styles.imagemiddleShadow,
                    { backgroundColor: BaseColor.card1 },
                  ]}>
                  <View style={{ marginBottom: 20 }}>
                    <View
                      activeOpacity={0.9}
                      // onPress={image}
                      style={[
                        styles.imageView,
                        { backgroundColor: BaseColor.whiteColor },
                      ]}>
                      <View>
                        <Image
                          style={styles.selectedImage}
                          source={{ uri: deviceDetail?.device_image }}
                          // source={require('../../assets/images/8.jpg')}
                        />
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </Animated.View>

            <View style={{ flex: 1, justifyContent: 'flex-end' }}>
              <Animated.View
                style={[
                  styles.infoView,
                  { top: 200, backgroundColor: BaseColor.infoView },
                  infoStyleAnim,
                ]}>
                <View
                  style={[
                    styles.horizontalLine,
                    { backgroundColor: BaseColor.black30 },
                  ]}
                />
                <Text
                  style={[styles.infoText, { color: BaseColor.whiteColor }]}>
                  {translate('children')}
                </Text>
                <DropDown
                  placeholder={translate('selectChild')}
                  data={childArr}
                  style={{ borderRadius: 12, flex: 1, marginEnd: 4 }}
                  valueProp="nick_name"
                  onSelect={val => {
                    // setHeight(val);
                    setstate({ ...state, height: val });
                  }}
                  selectedObject={state.height}
                />
                <View style={{ flexDirection: 'row', marginBottom: 16 }}>
                  <CButton
                    title={translate('assignDevice')}
                    style={{
                      backgroundColor: BaseColor.whiteColor,
                      borderRadius: 8,
                      marginTop: 16,
                      marginEnd: 4,
                      flex: 1,
                    }}
                    titleStyle={{
                      color: BaseColor.blackColor,
                      fontWeight: 'bold',
                    }}
                    loader={btnLoad === 'edit'}
                    onPress={() => {
                      editDevice();
                    }}
                  />
                  <CButton
                    title={translate('deleteDevice')}
                    style={{
                      backgroundColor: BaseColor.alertRed,
                      borderRadius: 8,
                      marginTop: 16,
                      marginStart: 4,
                      flex: 1,
                    }}
                    titleStyle={{
                      color: BaseColor.whiteColor,
                      fontWeight: 'bold',
                    }}
                    loader={btnLoad === 'delete'}
                    onPress={() => {
                      deleteDevice();
                    }}
                  />
                </View>
              </Animated.View>
            </View>
          </SafeAreaView>
        </ScrollView>
      </KeyboardAvoidingView>
      <Modal
        visible={loader}
        transparent
        style={{
          flex: 1,
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: BaseColor.black30,
          }}>
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: 'center',
              alignItems: 'center',
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}>
            <ActivityIndicator size={24} color={BaseColor.blueDark} />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: 'bold',
                marginTop: 8,
              }}>
              Loading
            </Text>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default EditDevice;
