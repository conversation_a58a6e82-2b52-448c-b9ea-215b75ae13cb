/* eslint-disable quotes */
import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  StatusBar,
  TouchableOpacity,
  BackHandler,
  Platform,
  ActivityIndicator,
  Modal,
} from 'react-native';
import Toast from 'react-native-simple-toast';
import { useTheme } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { isEmpty, isObject } from 'lodash';
import styles from './styles';
import { CustomIcon } from '../../config/LoadIcons';
import { translate } from '../../lang/Translate';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';
import { sendErrorReport } from '../../utils/commonFunction';
import StepPopup from '../../components/StepPopup';
import AuthAction from '../../redux/reducers/auth/actions';
import BluetoothAction from '../../redux/reducers/bluetooth/actions';

const Connect = ({ navigation, route }) => {
  const fromChildInfo = route?.params?.fromChildInfo;
  console.log('from child----1', fromChildInfo);

  const colors = useTheme();
  const BaseColor = colors.colors;
  const dispatch = useDispatch();
  const token = useSelector(state => state.auth.accessToken);
  const { isConnecting } = useSelector(state => state.bluetooth);
  const [showStep6, setShowStep6] = useState(false);

  const { setStep6Done } = AuthAction;
  const { step6Done, closeOnboarding } = useSelector(state => state.auth);
  const [loader, setloader] = useState(false);
  const { characteristicID, serviceID } = useSelector(state => state.bluetooth);
  const { setOnboardingDone } = AuthAction;

  function handleBackButtonClick() {
    Toast.show("Can't go back!");
    return true;
  }

  useEffect(() => {
    if (!step6Done && !closeOnboarding) {
      setShowStep6(true);
    }
  }, []);

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    getChildInfo();
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);
  useEffect(() => {
    const updateOrNavigate = async () => {
      if (!isConnecting) {
        // if (route?.params?.fromChildInfo) {
        //   console.log('routess_p_id', route?.params?.product_id);
        //   console.log('routess_d_id', route?.params?.device_id);
        //   console.log('routess_device_data', route?.params?.device_data);
        //   console.log('routess_device_ssid', route?.params?.device_ssid);
        //   console.log('routess_device--000-', devices[0]);
        //   await childProfile();
        // } else {
        navigation.navigate('ChildInfo', {
          type: 'connect',
          product_id: route?.params?.product_id,
          device_id: route?.params?.device_id,
          item: devices[0],
          device_data: route?.params?.device_data,
          device_ssid: route?.params?.device_ssid,
        });
      }
      // }
    };

    updateOrNavigate();
  }, [isConnecting]);

  const [devices, setDevices] = useState([
    {
      type: 'add',
    },
  ]);
  // this function for add use product
  /** this function for add use product
   * @function addUserProduct
   * @param {object} data child_id, device_id, product_id, service_id, characteristic_id, type, platform
   */
  async function addUserProduct(data) {
    const obj = {
      child_id: data?.id,
      device_id: route?.params?.device_id,
      product_id: route?.params?.product_id,
      device_data: route?.params?.device_data,
      device_ssid: route?.params?.device_ssid,
      service_id: serviceID,
      characteristic_id: characteristicID,
      type: 'type 1',
      platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
    };
    console.log('bhjhjhjhj----', obj);
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.addUserProduct,
        'POST',
        obj,
        headers,
      );

      if (response.success) {
        dispatch(setOnboardingDone(true));
        navigation.navigate('DrawerNav');
      }
    } catch (error) {
      console.log('error ===', error);
      sendErrorReport(error, 'add_user_prod_connect');
    }
  }
  /** this function for add/update child Profile
   * @function childProfile
   * @param {object} data nick_name, date_of_birth, height, weight, gender, child_profile, emergency_name, emergency_phone, emergency_phone_code, country
   */
  // const childProfile = async () => {
  //   console.log('CHILD PROFILE CALLED');
  //   setloader(true);
  //   const data = devices[0];
  //   console.log('data----childProfile', data);

  //   if (devices[0]?.id) {
  //     data.child_id = devices[0].id;
  //   }
  //   console.log('routess__ddd----', data);
  //   console.log('fjjf device id----', route?.params?.device_id);
  //   if (route?.params?.device_id) {
  //     data.device_id = route?.params?.device_id;
  //   }
  //   if (route?.params?.product_id) {
  //     data.product_id = route?.params?.product_id;
  //   }
  //   if (route?.params?.device_data) {
  //     data.device_data = route?.params?.device_data;
  //   }

  //   if (route?.params?.device_ssid) {
  //     data.device_ssid = route?.params?.device_ssid;
  //   }
  //   console.log('routess__ddd--all--', data);
  //   const headers = {
  //     'Content-Type': 'application/json',
  //     authorization: token ? `Bearer ${token}` : '',
  //   };

  //   const url = BaseSetting.endpoints.updateChild;
  //   data.about = undefined;
  //   data.stroller_name = undefined;

  //   getApiData(url, 'POST', data, headers)
  //     .then(response => {
  //       console.log('RESPONSE---CONNECT CHILD PROFILE-->>>>>>', response);
  //       if (response.success) {
  //         if (isObject(response.data) && !isEmpty(response.data)) {
  //           dispatch(BluetoothAction.setActiveChildDetail(response.data));
  //         }

  //         if (isObject(response.data) && !isEmpty(response.data)) {
  //           console.log('routess_add user pr');
  //           addUserProduct(response.data);
  //           // dispatch(BluetoothAction.setActiveDeviceId(response.data));
  //         }
  //       } else {
  //         Toast.show(response.message);
  //       }
  //       setloader(false);
  //     })
  //     .catch(err => {
  //       console.log('ERRR', err);
  //       Toast.show('Something went wrong! Unable to save child profile');
  //       sendErrorReport(err, 'add_update_child_profile_connect_s');
  //       setloader(false);
  //     });
  // };

  const childProfile = async () => {
    console.log('CHILD PROFILE CALLED');
    setloader(true);
    const data = devices[0];

    if (devices[0]?.id) {
      data.child_id = devices[0].id;
    }
    console.log('routess__ddd----', data);
    console.log('fjjf device id----', route?.params?.device_id);
    if (route?.params?.device_id) {
      data.device_id = route?.params?.device_id;
    }
    if (route?.params?.product_id) {
      data.product_id = route?.params?.product_id;
    }
    if (route?.params?.device_data) {
      data.device_data = route?.params?.device_data;
    }

    if (route?.params?.device_ssid) {
      data.device_ssid = route?.params?.device_ssid;
    }
    console.log('routess__ddd--all--', data);
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    const url = BaseSetting.endpoints.updateChild;
    data.about = undefined;
    data.stroller_name = undefined;

    try {
      const response = await getApiData(url, 'POST', data, headers);
      console.log('RESPONSE---CONNECT CHILD PROFILE-->>>>>>', response);
      if (response.success) {
        if (isObject(response.data) && !isEmpty(response.data)) {
          dispatch(BluetoothAction.setActiveChildDetail(response.data));
        }

        if (isObject(response.data) && !isEmpty(response.data)) {
          console.log('routess_add user pr');
          await addUserProduct(response.data);
          // dispatch(BluetoothAction.setActiveDeviceId(response.data));
        }
      } else {
        Toast.show(response.message);
      }
      setloader(false);
    } catch (err) {
      console.log('ERRR', err);
      Toast.show('Something went wrong! Unable to save child profile');
      sendErrorReport(err, 'add_update_child_profile_connect_s');
      setloader(false);
    }
  };
  /** this function for get Child Information
   * @function getChildInfo
   * @param {object} data {}
   */
  const getChildInfo = () => {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    console.log('getChildInfo -> headers', headers);

    getApiData(
      BaseSetting.endpoints.getUserChild,
      'POST',
      {
        platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      },
      headers,
    )
      .then(response => {
        if (response.success) {
          const tempArr = [
            {
              type: 'add',
            },
          ];
          const childArr = response.data;
          childArr.map(item => {
            tempArr.unshift(item);
          });

          setDevices(tempArr);
          console.log('aaaaaaaaaeeerrr---', tempArr[0]);
        } else {
          Toast.show(response.message);
        }
      })
      .catch(err => {
        // console.log("ERRR", err);
        // Toast.show('Something went wrong while getting child detail');
        sendErrorReport(err, 'get_child_in_device2');
      });
  };

  return (
    <View style={styles.root}>
      {/* <GradientBack /> */}
      <View style={styles.root}>
        <StatusBar backgroundColor="transparent" barStyle="dark-content" />
        <View
          style={[styles.imageView, { backgroundColor: BaseColor.whiteColor }]}>
          <CustomIcon
            name="Smartphone2"
            size={100}
            color={BaseColor.org}
            style={styles.deviceIcon}
          />
        </View>

        <Text style={[styles.connectText, { color: BaseColor.blackColor }]}>
          {isConnecting ? translate('connecting') : translate('connectScreen')}
        </Text>
        <Text style={[styles.otherText, { color: BaseColor.blackColor }]}>
          {isConnecting ? '' : translate('connectSuccessText')}
        </Text>

        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => {
            if (!isConnecting) {
              // if (route?.params?.fromChildInfo) {
              //   // update child and add user profile call do not navigate to child profile
              //   console.log('routess_p_id', route?.params?.product_id);
              //   console.log('routess_d_id', route?.params?.device_id);
              //   console.log('routess_device_data', route?.params?.device_data);
              //   console.log('routess_device_ssid', route?.params?.device_ssid);
              //   console.log('routess_device--000-', devices[0]);
              //   childProfile();
              // } else {
              navigation.navigate('ChildInfo', {
                type: 'connect',
                product_id: route?.params?.product_id,
                device_id: route?.params?.device_id,
                item: devices[0],
                device_data: route?.params?.device_data,
                device_ssid: route?.params?.device_ssid,
              });
              // }
            }
          }}
          style={[styles.checkIconView, { backgroundColor: BaseColor.org }]}>
          {isConnecting ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <CustomIcon
              name="check"
              size={20}
              color={BaseColor.whiteColor}
              style={styles.checkIcon}
            />
          )}
        </TouchableOpacity>
      </View>
      <StepPopup
        visible={showStep6}
        descriptionText={translate('step6')}
        image={require('../../assets/images/step6.png')}
        onNext={() => {
          setShowStep6(false);
          dispatch(setStep6Done(true));
        }}
        step6
      />
      <Modal
        visible={loader}
        transparent
        style={{
          flex: 1,
        }}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: BaseColor.black30,
          }}>
          <View
            style={{
              backgroundColor: BaseColor.whiteColor,
              justifyContent: 'center',
              alignItems: 'center',
              padding: 24,
              paddingHorizontal: 32,
              borderRadius: 24,
            }}>
            <ActivityIndicator size={24} color={BaseColor.blueDark} />
            <Text
              style={{
                color: BaseColor.blackColor,
                fontWeight: 'bold',
                marginTop: 8,
              }}>
              Loading
            </Text>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default Connect;
